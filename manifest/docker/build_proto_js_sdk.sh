#!/bin/bash

#
# 制作pb，制作flutter_proto_lib
#

# 安装 Dart 插件（用于生成 Dart 代码）
# 安装dart sdk
# dart pub global activate protoc_plugin
# export PATH="$PATH":"$HOME/.pub-cache/bin":"$HOME/workspace4nu/flutter/bin"

ROOT_DIR=$(pwd)

function gen_js_lib() {
  SVC_PATH=$1
  NAME=$2
  FLUTTER_PROTO_LIB_DIR=$3
  DST_PATH=$3/js/${NAME}
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    # PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto" -path "$SVC_PATH/manifest/protobuf/pbentity/*" -prune -o -name "*.proto" -print)
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    protoc --proto_path=$SVC_PATH/manifest/protobuf  \
            --proto_path=/usr/local/include \
            --js_out=import_style=commonjs,binary:$DST_PATH \
            $PROTO_FILES

    cd $FLUTTER_PROTO_LIB_DIR && find lib/${NAME} -name "*.pb.dart" | sort | sed 's|lib/|export '\''|g' | sed "s|\$|';|g" > lib/${NAME}.dart
}

OUTDIR=$ROOT_DIR/flutter_proto_lib
test -d $OUTDIR || mkdir $OUTDIR
rm -rf ${OUTDIR}/js
for NAME in `ls app`; do
  # 微服务目录
  SVC_PATH=$ROOT_DIR/app/$NAME

  gen_js_lib $SVC_PATH ${NAME%-svc} ${OUTDIR}
done
