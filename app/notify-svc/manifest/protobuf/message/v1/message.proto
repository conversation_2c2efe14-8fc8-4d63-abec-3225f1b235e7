syntax = "proto3";

package message.v1;
option go_package = "halalplus/app/notify-svc/api/message/v1;messagev1";
import "common/base.proto";


// 消息列表请求
message MessageListReq {
  common.PageRequest page = 1;  // 分页参数
}
// 消息列表响应
message MessageListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  MessageListData data = 4;
}

message MessageListData {
  common.PageResponse page = 1;  // 分页参数
  repeated  MessageListItem list = 2;
}
message MessageListItem {
  uint32 id = 1;
  uint32 message_type = 2; // 消息类型 1群发 2私聊
  uint32 sender_id = 3; // 发送者ID
  string title = 4; // 标题
  uint64 create_time =  5;
  uint32 is_read = 6; // 是否已读
  string content = 7; // 内容
}
// 是否有未读消息请求
message HasUnreadReq {
}

// 是否有未读消息响应
message HasUnreadRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HasUnreadData data = 4;
}
message HasUnreadData {
  bool has_unread = 1;
}


// 删除消息请求
message MessageDeleteReq{
  uint32 id = 1;// id
}
// 删除消息响应
message MessageDeleteRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}
// 消息服务
service MessageService {

  //是否有未读消息
  //  POST  /api/notify/message/v1/MessageService/HasUnread
  rpc HasUnread(HasUnreadReq) returns (HasUnreadRes);

  // 消息详情
  // Get /api/notify/message/v1/MessageService/MessageOne
  rpc MessageOne(MessageOneReq) returns (MessageOneRes);


  // 获取我的消息列表
  // Get /api/notify/message/v1/MessageService/GetMessageList
  rpc GetMessageList(GetMessageListReq) returns (GetMessageListRes);

  // 标记消息为已读
  // POST /api/notify/message/v1/MessageService/MarkMessageRead
  rpc MarkMessageRead(MarkMessageReadReq) returns (MarkMessageReadRes);

  // 删除消息
  // POST /api/notify/message/v1/MessageService/DeleteMessage
  rpc DeleteMessage(DeleteMessageReq) returns (DeleteMessageRes);

}

// 单条消息详情请求
message MessageOneReq {
  uint32 id = 1;
}
// 单条消息详情响应
message MessageOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UserMessage data = 4;
}


message DeleteMessageReq {
  repeated uint64 ids = 1;
}

message DeleteMessageRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message MarkMessageReadReq {
  repeated uint64 ids = 1; // 可支持批量标记
}

message MarkMessageReadRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message GetMessageListReq {
  common.PageRequest page = 4;  // 分页参数
}

message GetMessageListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  GetMessageListResData data = 4;
}

message GetMessageListResData {
  repeated UserMessage list = 1;
  common.PageResponse page = 2;  // 分页参数
}

// 我的消息分类
message UserMessageType {
  int32  Id          = 1; // 消息分类ID
  string Name        = 2; // 分类名字,在我的消息里《标题》
  string IconUrl     = 3; // 分类图标，标题《图标》
}

// content类型
enum UserMessageContentType {
  UNSPECIFIED = 0; // 默认值（建议保留）
  TEXT = 1;           // 文字消息
}

message UserMessage {
  uint64 id          = 1; // 消息唯一id
  UserMessageType  message_type = 3; // 消息分类
  UserMessageContentType  content_type = 4; // 1文字
  bytes  content     = 5; // 内容，这字段在json返回是base64格式，根据content_type解码
  int64  read_time    = 6; // 阅读时间，0则标识未读
  int64  created_time = 7; // 创建时间就是发送时间
  string title = 8;// 标题
}


