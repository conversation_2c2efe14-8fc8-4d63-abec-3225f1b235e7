syntax = "proto3";

package ws.v1;
option go_package = "halalplus/app/notify-svc/api/ws/v1";
import "common/base.proto";
import "common/front_info.proto";

// MessageEnvelope 是所有 WebSocket 通信的顶级消息信封
// 通过将消息类型分为 C2S (Client to Server) 和 S2C (Server to Client)，用于区分消息是由谁发出的
message MessageEnvelope {
  oneof payload {
    // 客户端 -> 服务器 消息
    // 将 C2S 消息类型从 1 开始
    // 所有认证相关的消息放在 1-10 之间
    C2SAuthMessage c2s_auth_message = 1; // 发送登录信息

    // 服务器 -> 客户端 消息
    // S2C 消息类型从 1001 开始
    S2CSystemNotification s2c_system_notification = 1000; // 服务器发送的系统通知
  }
}


// --- 具体的业务消息定义 ---

// 客户端发送给服务器的聊天消息
message C2SAuthMessage {
  string token = 1; // 登录返回的jwt token
  uint64 user_id = 2; // 用户ID
  common.FrontInfo front_info = 3; // FrontInfo
}

// 服务器发送给客户端的系统通知
message S2CAck {
}

message S2CSystemNotification {
  string title = 1;
  string message = 2;
  uint64 timestamp = 3;
}
