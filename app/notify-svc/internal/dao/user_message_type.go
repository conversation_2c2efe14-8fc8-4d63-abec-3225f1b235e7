// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/notify-svc/internal/dao/internal"
)

// userMessageTypeDao is the data access object for the table user_message_type.
// You can define custom methods on it to extend its functionality as needed.
type userMessageTypeDao struct {
	*internal.UserMessageTypeDao
}

var (
	// UserMessageType is a globally accessible object for table user_message_type operations.
	UserMessageType = userMessageTypeDao{internal.NewUserMessageTypeDao()}
)

// Add your custom methods and functionality below.
