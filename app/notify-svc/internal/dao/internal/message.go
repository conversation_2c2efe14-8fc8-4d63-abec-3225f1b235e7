// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MessageDao is the data access object for the table message.
type MessageDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  MessageColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// MessageColumns defines and stores column names for the table message.
type MessageColumns struct {
	Id            string //
	MessageMode   string // 消息模式 1群发 2私聊
	MessageTypeId string // 消息类型id
	SenderId      string // 发送人员
	Title         string // 标题
	ContentType   string // 1文字
	Content       string // 内容
	SendNums      string // 发送人数
	ReadNums      string // 已读人数
	CreateTime    string // 创建时间
	UpdateTime    string // 更新时间
}

// messageColumns holds the columns for the table message.
var messageColumns = MessageColumns{
	Id:            "id",
	MessageMode:   "message_mode",
	MessageTypeId: "message_type_id",
	SenderId:      "sender_id",
	Title:         "title",
	ContentType:   "content_type",
	Content:       "content",
	SendNums:      "send_nums",
	ReadNums:      "read_nums",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
}

// NewMessageDao creates and returns a new DAO object for table data access.
func NewMessageDao(handlers ...gdb.ModelHandler) *MessageDao {
	return &MessageDao{
		group:    "default",
		table:    "message",
		columns:  messageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MessageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MessageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MessageDao) Columns() MessageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MessageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MessageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MessageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
