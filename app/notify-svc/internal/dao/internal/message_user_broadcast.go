// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MessageUserBroadcastDao is the data access object for the table message_user_broadcast.
type MessageUserBroadcastDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  MessageUserBroadcastColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// MessageUserBroadcastColumns defines and stores column names for the table message_user_broadcast.
type MessageUserBroadcastColumns struct {
	Id              string //
	BroadcastType   string // 广播类型 1 站内信
	UserId          string // 用户id
	LastBroadcastId string // 上一次的广播id
	CreateTime      string // 创建时间
	UpdateTime      string // 更新时间
}

// messageUserBroadcastColumns holds the columns for the table message_user_broadcast.
var messageUserBroadcastColumns = MessageUserBroadcastColumns{
	Id:              "id",
	BroadcastType:   "broadcast_type",
	UserId:          "user_id",
	LastBroadcastId: "last_broadcast_id",
	CreateTime:      "create_time",
	UpdateTime:      "update_time",
}

// NewMessageUserBroadcastDao creates and returns a new DAO object for table data access.
func NewMessageUserBroadcastDao(handlers ...gdb.ModelHandler) *MessageUserBroadcastDao {
	return &MessageUserBroadcastDao{
		group:    "default",
		table:    "message_user_broadcast",
		columns:  messageUserBroadcastColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MessageUserBroadcastDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MessageUserBroadcastDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MessageUserBroadcastDao) Columns() MessageUserBroadcastColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MessageUserBroadcastDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MessageUserBroadcastDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MessageUserBroadcastDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
