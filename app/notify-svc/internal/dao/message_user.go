// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/notify-svc/internal/dao/internal"
)

// messageUserDao is the data access object for the table message_user.
// You can define custom methods on it to extend its functionality as needed.
type messageUserDao struct {
	*internal.MessageUserDao
}

var (
	// MessageUser is a globally accessible object for table message_user operations.
	MessageUser = messageUserDao{internal.NewMessageUserDao()}
)

// Add your custom methods and functionality below.
