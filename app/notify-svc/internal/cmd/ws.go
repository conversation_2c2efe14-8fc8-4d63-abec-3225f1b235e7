package cmd

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gsvc"
	"halalplus/app/notify-svc/internal/service"
)

func runWebsocketServer() {
	s := g.Server()
	// Bind WebSocket handler to /ws endpoint
	s.Bind<PERSON>andler("/ws", service.WsServer().Handler)

	// 注册发现
	s.SetName("notify-svc-websocket")
	reg := gsvc.GetRegistry()
	if !g.IsEmpty(reg) {
		s.SetRegistrar(gsvc.GetRegistry())
	}

	// Start the server
	s.Run()
}
