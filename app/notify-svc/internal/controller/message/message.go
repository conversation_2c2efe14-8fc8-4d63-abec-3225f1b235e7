package message

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/api/common"
	v1 "halalplus/app/notify-svc/api/message/v1"
	"halalplus/app/notify-svc/internal/service"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
)

type Controller struct {
	v1.UnimplementedMessageServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterMessageServiceServer(s.Server, &Controller{})
}
func SetDefaultPage(ctx context.Context, in *common.PageRequest) *common.PageRequest {
	if in == nil {
		in = &common.PageRequest{
			Page: 1,
			Size: 10,
		}
	}
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.Size <= 0 {
		in.Size = 10
	}
	return in
}

// 消息列表
func (*Controller) MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListRes, err error) {
	req.Page = SetDefaultPage(ctx, req.Page)
	res = &v1.MessageListRes{}
	data, err := service.Message().MessageList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res.Code = 200
	res.Msg = "success"
	res.Data = data

	return
}

// 是否有未读消息
func (*Controller) HasUnread(ctx context.Context, req *v1.HasUnreadReq) (res *v1.HasUnreadRes, err error) {
	res = &v1.HasUnreadRes{}
	unread, err := service.Message().HasUnread(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res.Code = 200
	res.Msg = "success"
	res.Data = unread
	return
}

// 消息详情
func (*Controller) MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageOneRes, err error) {
	res = &v1.MessageOneRes{}
	detail, err := service.Message().MessageOne(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res.Code = 200
	res.Msg = "success"
	res.Data = detail
	return
}

// 删除消息
func (*Controller) MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error) {
	res = &v1.MessageDeleteRes{}
	_, err = service.Message().MessageDelete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res.Code = 200
	res.Msg = "success"
	return
}

func (*Controller) GetMessageList(ctx context.Context, req *v1.GetMessageListReq) (res *v1.GetMessageListRes, err error) {
	req.Page = SetDefaultPage(ctx, req.Page)
	res = &v1.GetMessageListRes{}
	data, err := service.Message().GetMessageList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res.Code = 200
	res.Msg = "success"
	res.Data = data

	return

}

func (*Controller) MarkMessageRead(ctx context.Context, req *v1.MarkMessageReadReq) (res *v1.MarkMessageReadRes, err error) {
	res = &v1.MarkMessageReadRes{}
	if !g.IsEmpty(req.Ids) {
		_, err = service.Message().MarkMessageRead(ctx, req)
		if err != nil {
			return nil, err
		}
	}

	res.Code = 200
	res.Msg = "success"
	return
}

func (*Controller) DeleteMessage(ctx context.Context, req *v1.DeleteMessageReq) (res *v1.DeleteMessageRes, err error) {
	//res = &v1.DeleteMessageRes{}

	if !g.IsEmpty(req.Ids) {
		res, err = service.Message().DeleteMessage(ctx, req)
		if err != nil {
			g.Log().Error(ctx, err)
			return nil, err
		}
	}

	res.Code = 200
	res.Msg = "success"
	return
}
