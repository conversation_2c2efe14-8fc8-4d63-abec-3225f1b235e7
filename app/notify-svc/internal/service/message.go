// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/notify-svc/api/message/v1"
)

type (
	IMessage interface {
		HasUnread(ctx context.Context, req *v1.HasUnreadReq) (res *v1.HasUnreadData, err error)
		MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListData, err error)
		MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error)
		MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.UserMessage, err error)
		// 获取用户消息列表
		GetMessageList(ctx context.Context, req *v1.GetMessageListReq) (res *v1.GetMessageListResData, err error)
		// 标记用户消息已读
		MarkMessageRead(ctx context.Context, req *v1.MarkMessageReadReq) (res *v1.MarkMessageReadRes, err error)
		// 删除用户消息
		DeleteMessage(ctx context.Context, req *v1.DeleteMessageReq) (res *v1.DeleteMessageRes, err error)
	}
)

var (
	localMessage IMessage
)

func Message() IMessage {
	if localMessage == nil {
		panic("implement not found for interface IMessage, forgot register?")
	}
	return localMessage
}

func RegisterMessage(i IMessage) {
	localMessage = i
}
