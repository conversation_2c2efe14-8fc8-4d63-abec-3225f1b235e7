package service

import (
	"context"
	"github.com/gorilla/websocket"
	"google.golang.org/protobuf/proto"
)

type (
	IWsConnection interface {
		SendMessage(message proto.Message) error
		SendBinaryMessage(b []byte) error
		Close() error
		Ping() error
		Pong() error
		// 客户端授权信息
		SetAuth(userId uint64, deviceId string)
		IsAuth() bool
	}
	IWsManager interface {
		NewConnection(ctx context.Context, ws *websocket.Conn) IWsConnection
		Add(c IWsConnection) error
		Remove(conn IWsConnection)
		Broadcast(msg proto.Message) error
	}
)

var (
	localWsManager IWsManager
)

func WsManager() IWsManager {
	if localWsServer == nil {
		panic("implement not found for interface IWsServer, forgot register?")
	}
	return localWsManager
}

func RegisterWsManager(i IWsManager) {
	localWsManager = i
}
