// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IWsServer interface {
		// Handler 客户端连接
		Handler(r *ghttp.Request)
	}
)

var (
	localWsServer IWsServer
)

func WsServer() IWsServer {
	if localWsServer == nil {
		panic("implement not found for interface IWsServer, forgot register?")
	}
	return localWsServer
}

func RegisterWsServer(i IWsServer) {
	localWsServer = i
}
