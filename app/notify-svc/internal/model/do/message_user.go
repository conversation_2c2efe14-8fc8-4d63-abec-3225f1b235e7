// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// MessageUser is the golang structure of table message_user for DAO operations like Where/Data.
type MessageUser struct {
	g.Meta     `orm:"table:message_user, do:true"`
	Id         interface{} //
	MessageId  interface{} // 消息id
	UserId     interface{} // 收件人id
	IsRead     interface{} // 是否已读
	ReadTime   interface{} // 读件时间
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 更新时间
}
