// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Message is the golang structure of table message for DAO operations like Where/Data.
type Message struct {
	g.Meta        `orm:"table:message, do:true"`
	Id            interface{} //
	MessageMode   interface{} // 消息模式 1群发 2私聊
	MessageTypeId interface{} // 消息类型id
	SenderId      interface{} // 发送人员
	Title         interface{} // 标题
	ContentType   interface{} // 1文字
	Content       []byte      // 内容
	SendNums      interface{} // 发送人数
	ReadNums      interface{} // 已读人数
	CreateTime    interface{} // 创建时间
	UpdateTime    interface{} // 更新时间
}
