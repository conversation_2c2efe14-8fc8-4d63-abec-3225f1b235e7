// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserMessageType is the golang structure of table user_message_type for DAO operations like Where/Data.
type UserMessageType struct {
	g.Meta      `orm:"table:user_message_type, do:true"`
	Id          interface{} //
	Name        interface{} // 分类名字
	IconUrl     interface{} // 分类图标
	CreatedTime interface{} // 创建时间
	UpdatedTime interface{} // 更新时间
}
