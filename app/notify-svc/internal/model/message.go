package model

import "halalplus/app/notify-svc/internal/model/entity"

type MessageListItem struct {
	Id            uint               `json:"id"         orm:"id"          description:""`              //
	MessageTypeId int                `json:"messageTypeId" orm:"message_type_id" description:"消息类型id"` // 消息类型id
	Title         string             `json:"title"         orm:"title"           description:"标题"`     // 标题
	Content       []byte             `json:"content"       orm:"content"         description:"内容"`     // 内容
	ReadTime      int64              `json:"readTime"   orm:"read_time"   description:"读件时间"`          // 读件时间
	CreateTime    int64              `json:"createTime" orm:"create_time" description:"创建时间"`          // 创建时间
	MessageType   entity.MessageType `json:"messageType" orm:"with:id=message_type_id"`                // 消息类型
	ContentType   int                `json:"contentType"   orm:"content_type"    description:"1文字"`    // 1文字
}
