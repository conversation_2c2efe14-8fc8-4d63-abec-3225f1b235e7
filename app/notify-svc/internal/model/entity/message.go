// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Message is the golang structure for table message.
type Message struct {
	Id            uint   `json:"id"            orm:"id"              description:""`             //
	MessageMode   int    `json:"messageMode"   orm:"message_mode"    description:"消息模式 1群发 2私聊"` // 消息模式 1群发 2私聊
	MessageTypeId int    `json:"messageTypeId" orm:"message_type_id" description:"消息类型id"`       // 消息类型id
	SenderId      int    `json:"senderId"      orm:"sender_id"       description:"发送人员"`         // 发送人员
	Title         string `json:"title"         orm:"title"           description:"标题"`           // 标题
	ContentType   int    `json:"contentType"   orm:"content_type"    description:"1文字"`          // 1文字
	Content       []byte `json:"content"       orm:"content"         description:"内容"`           // 内容
	SendNums      int    `json:"sendNums"      orm:"send_nums"       description:"发送人数"`         // 发送人数
	ReadNums      int    `json:"readNums"      orm:"read_nums"       description:"已读人数"`         // 已读人数
	CreateTime    int64  `json:"createTime"    orm:"create_time"     description:"创建时间"`         // 创建时间
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"     description:"更新时间"`         // 更新时间
}
