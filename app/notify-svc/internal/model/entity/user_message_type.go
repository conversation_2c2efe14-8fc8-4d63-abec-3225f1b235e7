// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserMessageType is the golang structure for table user_message_type.
type UserMessageType struct {
	Id          int    `json:"id"          orm:"id"           description:""`     //
	Name        string `json:"name"        orm:"name"         description:"分类名字"` // 分类名字
	IconUrl     string `json:"iconUrl"     orm:"icon_url"     description:"分类图标"` // 分类图标
	CreatedTime int64  `json:"createdTime" orm:"created_time" description:"创建时间"` // 创建时间
	UpdatedTime int64  `json:"updatedTime" orm:"updated_time" description:"更新时间"` // 更新时间
}
