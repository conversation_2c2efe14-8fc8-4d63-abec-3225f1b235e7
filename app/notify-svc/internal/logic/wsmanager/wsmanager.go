package wsmanager

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gorilla/websocket"
	"google.golang.org/protobuf/proto"
	"halalplus/app/notify-svc/internal/service"
	"sync"
	"sync/atomic"
	"time"
)

type aWsManager struct {
	// TODO：
	// userId是用户标识，一个用户支持多个设备deviceId登录
	// 限制每个设备deviceId只有一个客户端连接
	// 限制每个userId最多10个设备在线，超出限制则无法建立连接
	// 设计一个数据结构保存客户端Connection，可以通过deviceId获取Connection，也可以通过UserID获取所有的Connection和数量
	userConnMap   map[uint64]map[*Connection]struct{}
	connMap       map[*Connection]struct{}
	deviceConnMap map[string]*Connection
	connMu        sync.RWMutex

	ctx  context.Context
	once sync.Once

	deadlineMu sync.RWMutex
	// cachedDeadline 存储缓存的截止时间
	cachedDeadline time.Time

	// 当前连接数量
	total int64
}

var _ service.IWsManager = &aWsManager{}

func init() {
	instance := New()
	service.RegisterWsManager(instance)
}

func New() service.IWsManager {
	return &aWsManager{
		userConnMap:   make(map[uint64]map[*Connection]struct{}),
		connMap:       make(map[*Connection]struct{}),
		deviceConnMap: make(map[string]*Connection),
		ctx:           gctx.New(),
	}
}

func (s *aWsManager) NewConnection(ctx context.Context, ws *websocket.Conn) service.IWsConnection {
	// 定时重置超时时间
	s.once.Do(func() {
		go s.updateDeadLine()
	})
	if ws != nil {
		// 设置 Pong 消息的 handler
		ws.SetReadDeadline(s.getReadDeadLine())
		ws.SetPongHandler(func(appData string) error {
			// 收到 Pong 重置超时
			//g.Log().Debug(context.Background(), "Received Pong from client:", ws.RemoteAddr())
			ws.SetReadDeadline(s.getReadDeadLine()) // 比如，60秒内必须有数据
			return nil
		})
	}

	conn := &Connection{
		ctx:    ctx,
		wsConn: ws,
		userId: 0,
	}
	s.Add(conn)
	return conn
}

func (s *aWsManager) getReadDeadLine() time.Time {
	s.deadlineMu.Lock()
	defer s.deadlineMu.Unlock()
	return s.cachedDeadline
}

// 更新deadline时间
func (s *aWsManager) updateDeadLine() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		s.deadlineMu.Lock()
		s.cachedDeadline = time.Now().Add(60 * time.Second)
		s.deadlineMu.Unlock()
	}
}

// 新增连接
// 确保同一个设备只有一个连接客户端
// userId是用户标识，一个用户支持多个设备登录，设备用设备号deviceId区分
func (s *aWsManager) Add(c service.IWsConnection) error {
	s.connMu.Lock()
	defer s.connMu.Unlock()

	conn := c.(*Connection)
	if conn.IsClosed() {
		return errors.New("connection is closed")
	}
	if _, exists := s.connMap[conn]; !exists {
		s.connMap[conn] = struct{}{}
		atomic.AddInt64(&s.total, 1)
	}

	if c.IsAuth() {
		conns, ok := s.userConnMap[conn.userId]
		if !ok {
			conns = make(map[*Connection]struct{})
			s.userConnMap[conn.userId] = conns
		}
		conns[conn] = struct{}{}
	}

	g.Log().Debug(s.ctx, "客户端连接", conn.userId, len(s.userConnMap[conn.userId]), atomic.LoadInt64(&s.total))
	return nil
}

// 移除连接
func (s *aWsManager) Remove(c service.IWsConnection) {
	s.connMu.Lock()
	defer s.connMu.Unlock()

	conn := c.(*Connection)
	conn.Close()

	if _, exists := s.connMap[conn]; !exists {
		s.connMap[conn] = struct{}{}
		atomic.AddInt64(&s.total, -1)
	}

	if c.IsAuth() {
		conns, ok := s.userConnMap[conn.userId]
		if ok {
			delete(conns, conn)
			if len(conns) == 0 {
				delete(s.userConnMap, conn.userId)
			}
		}
	}

	g.Log().Debug(s.ctx, "客户端断开连接", conn.userId, len(s.userConnMap), atomic.LoadInt64(&s.total))
}

func (s *aWsManager) Broadcast(msg proto.Message) error {
	b, err := proto.Marshal(msg)
	if err != nil {
		g.Log().Error(s.ctx, err)
		return err
	}
	// Iterate over the outer map (user IDs).
	for _, connSet := range s.userConnMap {
		// For each user, iterate over their connections.
		for conn := range connSet {

			// Call the Send method for each connection in a new goroutine
			// to avoid blocking the main broadcast loop.
			err := conn.SendBinaryMessage(b)
			if err != nil {
				g.Log().Debug(s.ctx, "Broadcast", err)
			}
		}
	}
	return nil
}
