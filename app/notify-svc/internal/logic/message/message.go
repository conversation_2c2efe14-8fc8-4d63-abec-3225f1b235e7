package message

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/api/common"
	v1 "halalplus/app/notify-svc/api/message/v1"
	"halalplus/app/notify-svc/internal/consts"
	"halalplus/app/notify-svc/internal/dao"
	"halalplus/app/notify-svc/internal/model"
	"halalplus/app/notify-svc/internal/model/do"
	"halalplus/app/notify-svc/internal/model/entity"
	"halalplus/app/notify-svc/internal/service"
	"halalplus/utility/token"
	"time"
)

type sMessage struct{}

func init() {
	service.RegisterMessage(New())
}

func New() service.IMessage {
	return &sMessage{}
}

func (s *sMessage) HasUnread(ctx context.Context, req *v1.HasUnreadReq) (res *v1.HasUnreadData, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	// 站内信 是属于系统消息的。
	// 取出上次最大的广播messageId
	value, err := dao.MessageUserBroadcast.Ctx(ctx).
		Where(dao.MessageUserBroadcast.Columns().UserId, userId).
		Where(dao.MessageUserBroadcast.Columns().BroadcastType, consts.BroadcastTypeInternal).
		Fields(dao.MessageUserBroadcast.Columns().LastBroadcastId).Value()
	if err != nil {
		return nil, err
	}

	lastId := value.Int()
	// 查询是否有最新的群发消息。
	var entryList []*entity.Message
	err = dao.Message.Ctx(ctx).
		WhereGT(dao.Message.Columns().Id, lastId).
		Where(dao.Message.Columns().MessageMode, consts.MessageTypeBroadcast).
		Scan(&entryList)
	if err != nil {
		return nil, err
	}
	// 有群发消息后插入message_user表,更新 last_broadcast_id
	if len(entryList) > 0 {
		var maxId uint
		var messageUsers []*entity.MessageUser
		for _, entry := range entryList {
			messageUsers = append(messageUsers, &entity.MessageUser{
				UserId:    uint(userId),
				MessageId: entry.Id,
				IsRead:    consts.IsReadNo,
				ReadTime:  0,
			})
			if entry.Id > maxId {
				maxId = entry.Id
			}
		}
		err = dao.MessageUser.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			_, err = tx.Model(dao.MessageUser.Table()).Ctx(ctx).Insert(messageUsers)
			if err != nil {
				return err
			}

			_, err = tx.Model(dao.MessageUserBroadcast.Table()).Ctx(ctx).
				Data(g.Map{
					dao.MessageUserBroadcast.Columns().UserId:          userId,
					dao.MessageUserBroadcast.Columns().BroadcastType:   consts.BroadcastTypeInternal,
					dao.MessageUserBroadcast.Columns().LastBroadcastId: maxId,
				}).Save()
			return err
		})
		if err != nil {
			return nil, err
		}

	}
	// 查询是否有未读消息
	count, err := dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().IsRead, consts.IsReadNo).
		Count()
	if err != nil {
		return nil, err
	}
	res = &v1.HasUnreadData{
		HasUnread: count > 0,
	}
	return
}

func (s *sMessage) MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListData, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	mu := dao.MessageUser.Table()
	m := dao.Message.Table()

	// 先查询message user表。分页 获取message id
	md := dao.MessageUser.Ctx(ctx).LeftJoin(m, fmt.Sprintf("%s.id = %s.message_id", m, mu)).
		Where(mu+".user_id", userId).
		Order(mu + ".id DESC")
	count, err := md.Count()
	if err != nil {
		return nil, err
	}

	var messageListItems []*v1.MessageListItem
	err = md.Fields(
		fmt.Sprintf("%s.id", mu),
		fmt.Sprintf("%s.message_type", m),
		fmt.Sprintf("%s.sender_id", m),
		fmt.Sprintf("%s.title", m),
		fmt.Sprintf("%s.is_read", mu),
	).Page(int(req.Page.Page), int(req.Page.Size)).Scan(&messageListItems)
	if err != nil {
		return nil, err
	}

	res = &v1.MessageListData{
		List: messageListItems,
		Page: &common.PageResponse{
			Page:  req.Page.Page,
			Size:  req.Page.Size,
			Total: int32(count),
		},
	}
	return

}

func (s *sMessage) MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	_, err = dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().Id, req.Id).
		Delete()
	return
}
func (s *sMessage) MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.UserMessage, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	mu := dao.MessageUser.Table()
	m := dao.Message.Table()

	md := dao.MessageUser.Ctx(ctx).LeftJoin(m, fmt.Sprintf("%s.id = %s.message_id", m, mu)).
		Where(mu+".user_id", userId).
		Where(mu+".id", req.Id)
	var one *model.MessageListItem
	err = md.Fields(
		fmt.Sprintf("%s.id", mu),
		fmt.Sprintf("%s.message_type_id", m),
		fmt.Sprintf("%s.title", m),
		fmt.Sprintf("%s.content", m),
		fmt.Sprintf("%s.content_type", m),
		fmt.Sprintf("%s.read_time", mu),
		fmt.Sprintf("%s.create_time", m),
	).With(model.MessageListItem{}.MessageType).Scan(&one)
	if err != nil {
		return nil, err
	}

	err = gconv.Struct(one, &res)
	if err != nil {
		return nil, err
	}

	// 是后要改成已读 ？

	return

}

// 获取用户消息列表
func (*sMessage) GetMessageList(ctx context.Context, req *v1.GetMessageListReq) (res *v1.GetMessageListResData, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	mu := dao.MessageUser.Table()
	m := dao.Message.Table()

	// 先查询message user表。分页 获取message id
	md := dao.MessageUser.Ctx(ctx).LeftJoin(m, fmt.Sprintf("%s.id = %s.message_id", m, mu)).
		Where(mu+".user_id", userId).
		Order(mu + ".id DESC")
	count, err := md.Count()
	if err != nil {
		return nil, err
	}

	//message UserMessage {
	//	uint64 id          = 1; // 消息唯一id
	//	UserMessageType  message_type = 3; // 消息分类
	//	UserMessageContentType  content_type = 4; // 1文字
	//	bytes  content     = 5; // 内容，这字段在json返回是base64格式，根据content_type解码
	//	int64  read_time    = 6; // 阅读时间，0则标识未读
	//	int64  created_time = 7; // 创建时间就是发送时间
	//}

	var messageListItems []*model.MessageListItem
	err = md.Fields(
		fmt.Sprintf("%s.id", mu),
		fmt.Sprintf("%s.message_type_id", m),
		fmt.Sprintf("%s.title", m),
		fmt.Sprintf("%s.content", m),
		fmt.Sprintf("%s.content_type", m),
		fmt.Sprintf("%s.read_time", mu),
		fmt.Sprintf("%s.create_time", m),
	).With(model.MessageListItem{}.MessageType).Page(int(req.Page.Page), int(req.Page.Size)).Scan(&messageListItems)
	if err != nil {
		return nil, err
	}
	var outputs []*v1.UserMessage
	err = gconv.SliceStruct(&messageListItems, &outputs)
	if err != nil {
		return nil, err
	}
	res = &v1.GetMessageListResData{
		List: outputs,
		Page: &common.PageResponse{
			Page:  req.Page.Page,
			Size:  req.Page.Size,
			Total: int32(count),
		},
	}
	return
}

// 标记用户消息已读
func (*sMessage) MarkMessageRead(ctx context.Context, req *v1.MarkMessageReadReq) (res *v1.MarkMessageReadRes, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	messageUsers := make([]*entity.MessageUser, 0, len(req.Ids))
	err = dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().Id, req.Ids).
		Where(dao.MessageUser.Columns().IsRead, consts.IsReadNo).
		Scan(&messageUsers)

	if err != nil {
		return nil, err
	}
	// 要统计已读人数，精准
	ids := make([]uint, 0, len(messageUsers))
	messageIds := make([]uint, 0, len(messageUsers))
	for _, messageUser := range messageUsers {
		ids = append(ids, messageUser.Id)
		messageIds = append(messageIds, messageUser.MessageId)
	}

	if len(ids) > 0 {
		err = dao.MessageUser.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 更新已读
			_, err = tx.Model(dao.MessageUser.Table()).Where(dao.MessageUser.Columns().Id, ids).
				Update(do.MessageUser{
					IsRead:   consts.IsReadYes,
					ReadTime: time.Now().UnixMilli(),
				})
			if err != nil {
				return err
			}
			// 更新已读人数
			_, err = tx.Model(dao.Message.Table()).Where(dao.Message.Columns().Id, messageIds).Increment(dao.Message.Columns().ReadNums, 1)
			return err
		})
		if err != nil {
			return nil, err
		}
	}
	res = &v1.MarkMessageReadRes{}
	return
}

// 删除用户消息
func (*sMessage) DeleteMessage(ctx context.Context, req *v1.DeleteMessageReq) (res *v1.DeleteMessageRes, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	// 应该删除就可以了
	_, err = dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().Id, req.Ids).
		Delete()
	if err != nil {
		return nil, err
	}
	res = &v1.DeleteMessageRes{}
	return
}
