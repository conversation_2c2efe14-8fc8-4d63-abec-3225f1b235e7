package wsserver

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"google.golang.org/protobuf/proto"
	"halalplus/api/common"
	wsv1 "halalplus/app/notify-svc/api/ws/v1"
	"halalplus/app/notify-svc/internal/logic/wsmanager"
	"halalplus/app/notify-svc/internal/service"
	"reflect"
	"sync"
	"testing"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want service.IWsServer
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sWsServer_Handler(t *testing.T) {
	type fields struct {
		wsUpGrader *websocket.Upgrader
		once       sync.Once
	}
	type args struct {
		r *ghttp.Request
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sWsServer{
				wsUpGrader: tt.fields.wsUpGrader,
				once:       tt.fields.once,
			}
			s.Handler(tt.args.r)
		})
	}
}

func Test_sWsServer_dispatch(t *testing.T) {
	type fields struct {
		wsUpGrader *websocket.Upgrader
		once       sync.Once
	}
	type args struct {
		ctx  context.Context
		conn service.IWsConnection
		msg  []byte
	}

	msg, _ := proto.Marshal(&wsv1.MessageEnvelope{
		Payload: &wsv1.MessageEnvelope_C2SAuthMessage{
			C2SAuthMessage: &wsv1.C2SAuthMessage{
				Token:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eSI6InVzZXIiLCJzdWIiOiI1IiwiZXhwIjoxNzU2NTcwOTE1fQ.E9wgp7uCKLFKW59AMXA3cOmeO-J-QIZDeeNEHYEpTmw",
				UserId: 5,
				FrontInfo: &common.FrontInfo{
					DeviceId: uuid.New().String(),
				},
			},
		},
	})

	notAuthMsg, _ := proto.Marshal(&wsv1.MessageEnvelope{
		Payload: &wsv1.MessageEnvelope_S2CSystemNotification{
			S2CSystemNotification: &wsv1.S2CSystemNotification{
				Title: "hello",
			},
		},
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "auth",
			fields: fields{},
			args: args{
				ctx:  context.Background(),
				conn: &wsmanager.Connection{},
				msg:  msg,
			},
		},
		{
			name:   "test",
			fields: fields{},
			args: args{
				ctx:  context.Background(),
				conn: &wsmanager.Connection{},
				msg:  notAuthMsg,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sWsServer{
				wsUpGrader: tt.fields.wsUpGrader,
				once:       tt.fields.once,
			}
			if _, err := s.dispatch(tt.args.ctx, tt.args.conn, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("dispatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_sWsServer_handleReader(t *testing.T) {
	type fields struct {
		wsUpGrader *websocket.Upgrader
		once       sync.Once
	}
	type args struct {
		ctx  context.Context
		conn service.IWsConnection
		ws   *websocket.Conn
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sWsServer{
				wsUpGrader: tt.fields.wsUpGrader,
				once:       tt.fields.once,
			}
			s.handleReader(tt.args.ctx, tt.args.conn, tt.args.ws)
		})
	}
}

func Test_sWsServer_handle_C2SAuthMessage(t *testing.T) {
	type fields struct {
		wsUpGrader *websocket.Upgrader
		once       sync.Once
	}
	type args struct {
		ctx     context.Context
		conn    service.IWsConnection
		payload *wsv1.C2SAuthMessage
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sWsServer{
				wsUpGrader: tt.fields.wsUpGrader,
				once:       tt.fields.once,
			}
			if _, err := s.Handler_C2SAuthMessage(tt.args.ctx, tt.args.conn, tt.args.payload); (err != nil) != tt.wantErr {
				t.Errorf("handle_C2SAuthMessage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_sWsServer_testBroadcast(t *testing.T) {
	type fields struct {
		wsUpGrader *websocket.Upgrader
		once       sync.Once
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sWsServer{
				wsUpGrader: tt.fields.wsUpGrader,
				once:       tt.fields.once,
			}
			s.testBroadcast()
		})
	}
}
