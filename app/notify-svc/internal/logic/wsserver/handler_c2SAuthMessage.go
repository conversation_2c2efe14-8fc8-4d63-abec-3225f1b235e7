package wsserver

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	wsv1 "halalplus/app/notify-svc/api/ws/v1"
	"halalplus/app/notify-svc/internal/service"
	"halalplus/utility/token"
)

// Handler_C2SAuthMessage 登录授权，ws客户端连接后的第一个数据包
func (s *sWsServer) Handler_C2SAuthMessage(ctx context.Context, conn service.IWsConnection, payload *wsv1.C2SAuthMessage) (reply *wsv1.S2CAck, err error) {
	// 授权成功则加入
	// 失败则关闭
	g.Log().Debug(ctx, "Handler_C2SAuthMessage", payload)
	//conn.SendMessage(([]byte)"ok")
	uid, err := token.GetUserIdFromToken(ctx, payload.Token)
	if err != nil {
		g.Log().Error(ctx, err)
		conn.Close()
		return nil, err
	}
	if payload.FrontInfo == nil {
		g.Log().Debug(ctx, "payload.FrontInfo is nil")
		conn.Close()
		return nil, nil
	}
	g.Log().Debug(ctx, "uid", uid, payload.FrontInfo.DeviceId)

	// 连接的唯一性，用deviceId和uid区分
	conn.SetAuth(uid, payload.FrontInfo.DeviceId)
	err = service.WsManager().Add(conn)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	// 把连接放入链接池管理
	return nil, nil
}
