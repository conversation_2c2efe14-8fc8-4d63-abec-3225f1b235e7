package wsserver

import (
	wsv1 "halalplus/app/notify-svc/api/ws/v1"
	"halalplus/app/notify-svc/internal/service"
	"time"
)

// Broadcast_nofification 定时推送消息
func (s *sWsServer) broadcast_nofification() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		// 创建消息
		msg := &wsv1.MessageEnvelope{
			Payload: &wsv1.MessageEnvelope_S2CSystemNotification{
				S2CSystemNotification: &wsv1.S2CSystemNotification{
					Title:   "welcome",
					Message: "welcome to the system notification service",
				},
			},
		}
		//b = gconv.Bytes(msg)
		service.WsManager().Broadcast(msg)
	}
}
