package wsserver

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gorilla/websocket"
	"google.golang.org/protobuf/proto"
	wsv1 "halalplus/app/notify-svc/api/ws/v1"
	"halalplus/app/notify-svc/internal/service"
	"net/http"
	"reflect"
	"strings"
	"sync"
)

type sWsServer struct {
	wsUpGrader *websocket.Upgrader
	once       sync.Once
}

func init() {
	service.RegisterWsServer(New())
}

func New() service.IWsServer {
	return &sWsServer{
		wsUpGrader: &websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			// CheckOrigin allows any origin in development
			// In production, implement proper origin checking for security
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
			// Error handler for upgrade failures
			Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
				// Implement error handling logic here
				g.Log().Error(r.Context(), "websocket.Upgrader", status, reason)
			},
		},
	}
}

// Handler 客户端连接
func (s *sWsServer) Handler(r *ghttp.Request) {
	s.once.Do(func() {
		go s.broadcast_nofification()
	})
	// Upgrade HTTP connection to WebSocket
	ws, err := s.wsUpGrader.Upgrade(r.Response.Writer, r.Request, nil)
	if err != nil {
		r.Response.Write(err.Error())
		return
	}
	defer ws.Close()

	// Get request context for logging
	var ctx = r.Context()

	g.Log().Info(ctx, "websocket connected")

	conn := service.WsManager().NewConnection(ctx, ws)
	defer service.WsManager().Remove(conn)

	// 循环读取客户端消息
	s.handleReader(ctx, conn, ws)

	// Log connection closure
	g.Log().Info(ctx, "websocket connection closed")

}

// handleReader 循环读取客户端消息
func (s *sWsServer) handleReader(ctx context.Context, conn service.IWsConnection, ws *websocket.Conn) {
	// Message handling loop
	for {
		// Read incoming WebSocket message
		messageType, msg, err := ws.ReadMessage()
		if err != nil {
			break // Connection closed or error occurred
		}
		if messageType == websocket.BinaryMessage {
			g.Log().Debugf(ctx, "received BinaryMessage: %s", msg)
			// 调用 s.handler_XXX()处理
			var reply proto.Message
			reply, err = s.dispatch(ctx, conn, msg)
			if err != nil {
				g.Log().Error(ctx, err)
				continue
			}
			if reply == nil {

				g.Log().Debug(ctx, "reply message", reply)
				conn.SendMessage(reply)
			}
		} else if messageType == websocket.TextMessage {
			// Log received message
			g.Log().Infof(ctx, "received message: %s", msg)
		}
	}
}

// dispatch 通用分发方法，根据消息类型动态调用对应的处理器
// 参数msg例子 ： msg, _ := proto.Marshal(&wsv1.MessageEnvelope{
// Payload: &wsv1.MessageEnvelope_C2SAuthMessage{
// C2SAuthMessage: &wsv1.C2SAuthMessage{
// UserId: 232,
// },
// },
// })
func (s *sWsServer) dispatch(ctx context.Context, conn service.IWsConnection, msg []byte) (reply proto.Message, err error) {
	envelope := &wsv1.MessageEnvelope{}
	err = proto.Unmarshal(msg, envelope)
	if err != nil || envelope.Payload == nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 从消息中获取消息体的反射类型
	messageType := reflect.TypeOf(envelope.Payload).Elem().Name()
	payloadField := reflect.ValueOf(envelope.Payload).Elem().Field(0).Interface()

	// 构造方法名，例如 "HandleC2SAuthMessage"
	// 这里需要一些字符串处理逻辑来从 `MessageEnvelope_C2SAuthMessage` 得到 `C2SAuthMessage`
	// 然后再得到 `HandleC2SAuthMessage`
	methodName := fmt.Sprintf("Handler_%s", strings.TrimPrefix(messageType, "MessageEnvelope_"))

	// 4. 调用方法
	// Call 返回一个 reflect.Value 的切片，包含了方法的返回值
	g.Log().Debug(ctx, "wsServer.dispatch", methodName, gconv.Map(payloadField))
	if !conn.IsAuth() && methodName != "Handler_C2SAuthMessage" {
		// 没有登录，必须先发C2SAuthMessage
		//conn.SendMessage([]byte("C2SAuthMessage"))
		return nil, errors.New("not C2SAuthMessage")
	}

	// 2. 使用反射查找方法
	// reflect.ValueOf(s) 获取 sWsServer 的反射值
	handlerMethod := reflect.ValueOf(s).MethodByName(methodName)
	if !handlerMethod.IsValid() {
		g.Log().Error(ctx, "handler method is not valid", methodName)
		return nil, fmt.Errorf("handler not found for message type: %s", messageType)
	}

	// 3. 构建参数列表
	args := []reflect.Value{
		reflect.ValueOf(ctx),
		reflect.ValueOf(conn),
		reflect.ValueOf(payloadField),
	}
	results := handlerMethod.Call(args)

	// 5. 处理返回值, handlerMethod可以返回一个error，或者（proto.Message， error)
	reply = nil
	err = nil
	if len(results) > 1 {
		if !results[0].IsNil() {
			reply = results[0].Interface().(proto.Message)
		}
		if !results[1].IsNil() {
			err = results[1].Interface().(error)
		}
	} else if len(results) == 1 && !results[0].IsNil() {
		err = results[0].Interface().(error)
	}
	return reply, err
}
