// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: message/v1/message.proto

package messagev1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// content类型
type UserMessageContentType int32

const (
	UserMessageContentType_UNSPECIFIED UserMessageContentType = 0 // 默认值（建议保留）
	UserMessageContentType_TEXT        UserMessageContentType = 1 // 文字消息
)

// Enum value maps for UserMessageContentType.
var (
	UserMessageContentType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "TEXT",
	}
	UserMessageContentType_value = map[string]int32{
		"UNSPECIFIED": 0,
		"TEXT":        1,
	}
)

func (x UserMessageContentType) Enum() *UserMessageContentType {
	p := new(UserMessageContentType)
	*p = x
	return p
}

func (x UserMessageContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserMessageContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_v1_message_proto_enumTypes[0].Descriptor()
}

func (UserMessageContentType) Type() protoreflect.EnumType {
	return &file_message_v1_message_proto_enumTypes[0]
}

func (x UserMessageContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserMessageContentType.Descriptor instead.
func (UserMessageContentType) EnumDescriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{0}
}

// 消息列表请求
type MessageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *MessageListReq) Reset() {
	*x = MessageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListReq) ProtoMessage() {}

func (x *MessageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListReq.ProtoReflect.Descriptor instead.
func (*MessageListReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{0}
}

func (x *MessageListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 消息列表响应
type MessageListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *MessageListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MessageListRes) Reset() {
	*x = MessageListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListRes) ProtoMessage() {}

func (x *MessageListRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListRes.ProtoReflect.Descriptor instead.
func (*MessageListRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{1}
}

func (x *MessageListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MessageListRes) GetData() *MessageListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type MessageListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageResponse `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	List []*MessageListItem   `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MessageListData) Reset() {
	*x = MessageListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListData) ProtoMessage() {}

func (x *MessageListData) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListData.ProtoReflect.Descriptor instead.
func (*MessageListData) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{2}
}

func (x *MessageListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *MessageListData) GetList() []*MessageListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type MessageListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MessageType uint32 `protobuf:"varint,2,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty" dc:"消息类型 1群发 2私聊"` // 消息类型 1群发 2私聊
	SenderId    uint32 `protobuf:"varint,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty" dc:"发送者ID"`                 // 发送者ID
	Title       string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                                           // 标题
	CreateTime  uint64 `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsRead      uint32 `protobuf:"varint,6,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty" dc:"是否已读"` // 是否已读
	Content     string `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty" dc:"内容"`                // 内容
}

func (x *MessageListItem) Reset() {
	*x = MessageListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListItem) ProtoMessage() {}

func (x *MessageListItem) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListItem.ProtoReflect.Descriptor instead.
func (*MessageListItem) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{3}
}

func (x *MessageListItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MessageListItem) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *MessageListItem) GetSenderId() uint32 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *MessageListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MessageListItem) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MessageListItem) GetIsRead() uint32 {
	if x != nil {
		return x.IsRead
	}
	return 0
}

func (x *MessageListItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 是否有未读消息请求
type HasUnreadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HasUnreadReq) Reset() {
	*x = HasUnreadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasUnreadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadReq) ProtoMessage() {}

func (x *HasUnreadReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadReq.ProtoReflect.Descriptor instead.
func (*HasUnreadReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{4}
}

// 是否有未读消息响应
type HasUnreadRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string         `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error  `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *HasUnreadData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *HasUnreadRes) Reset() {
	*x = HasUnreadRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasUnreadRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadRes) ProtoMessage() {}

func (x *HasUnreadRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadRes.ProtoReflect.Descriptor instead.
func (*HasUnreadRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{5}
}

func (x *HasUnreadRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HasUnreadRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HasUnreadRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HasUnreadRes) GetData() *HasUnreadData {
	if x != nil {
		return x.Data
	}
	return nil
}

type HasUnreadData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasUnread bool `protobuf:"varint,1,opt,name=has_unread,json=hasUnread,proto3" json:"has_unread,omitempty"`
}

func (x *HasUnreadData) Reset() {
	*x = HasUnreadData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasUnreadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadData) ProtoMessage() {}

func (x *HasUnreadData) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadData.ProtoReflect.Descriptor instead.
func (*HasUnreadData) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{6}
}

func (x *HasUnreadData) GetHasUnread() bool {
	if x != nil {
		return x.HasUnread
	}
	return false
}

// 删除消息请求
type MessageDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"id"` // id
}

func (x *MessageDeleteReq) Reset() {
	*x = MessageDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDeleteReq) ProtoMessage() {}

func (x *MessageDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDeleteReq.ProtoReflect.Descriptor instead.
func (*MessageDeleteReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{7}
}

func (x *MessageDeleteReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除消息响应
type MessageDeleteRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *MessageDeleteRes) Reset() {
	*x = MessageDeleteRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageDeleteRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDeleteRes) ProtoMessage() {}

func (x *MessageDeleteRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDeleteRes.ProtoReflect.Descriptor instead.
func (*MessageDeleteRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{8}
}

func (x *MessageDeleteRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageDeleteRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageDeleteRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 单条消息详情请求
type MessageOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *MessageOneReq) Reset() {
	*x = MessageOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageOneReq) ProtoMessage() {}

func (x *MessageOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageOneReq.ProtoReflect.Descriptor instead.
func (*MessageOneReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{9}
}

func (x *MessageOneReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 单条消息详情响应
type MessageOneRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *UserMessage  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MessageOneRes) Reset() {
	*x = MessageOneRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageOneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageOneRes) ProtoMessage() {}

func (x *MessageOneRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageOneRes.ProtoReflect.Descriptor instead.
func (*MessageOneRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{10}
}

func (x *MessageOneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageOneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageOneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MessageOneRes) GetData() *UserMessage {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteMessageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteMessageReq) Reset() {
	*x = DeleteMessageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMessageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageReq) ProtoMessage() {}

func (x *DeleteMessageReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageReq.ProtoReflect.Descriptor instead.
func (*DeleteMessageReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteMessageReq) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteMessageRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DeleteMessageRes) Reset() {
	*x = DeleteMessageRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMessageRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageRes) ProtoMessage() {}

func (x *DeleteMessageRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageRes.ProtoReflect.Descriptor instead.
func (*DeleteMessageRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteMessageRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteMessageRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DeleteMessageRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type MarkMessageReadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty" dc:"可支持批量标记"` // 可支持批量标记
}

func (x *MarkMessageReadReq) Reset() {
	*x = MarkMessageReadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkMessageReadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMessageReadReq) ProtoMessage() {}

func (x *MarkMessageReadReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMessageReadReq.ProtoReflect.Descriptor instead.
func (*MarkMessageReadReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{13}
}

func (x *MarkMessageReadReq) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type MarkMessageReadRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *MarkMessageReadRes) Reset() {
	*x = MarkMessageReadRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkMessageReadRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMessageReadRes) ProtoMessage() {}

func (x *MarkMessageReadRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMessageReadRes.ProtoReflect.Descriptor instead.
func (*MarkMessageReadRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{14}
}

func (x *MarkMessageReadRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MarkMessageReadRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MarkMessageReadRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetMessageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *GetMessageListReq) Reset() {
	*x = GetMessageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageListReq) ProtoMessage() {}

func (x *GetMessageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageListReq.ProtoReflect.Descriptor instead.
func (*GetMessageListReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{15}
}

func (x *GetMessageListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type GetMessageListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *GetMessageListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMessageListRes) Reset() {
	*x = GetMessageListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageListRes) ProtoMessage() {}

func (x *GetMessageListRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageListRes.ProtoReflect.Descriptor instead.
func (*GetMessageListRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{16}
}

func (x *GetMessageListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMessageListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMessageListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetMessageListRes) GetData() *GetMessageListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetMessageListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserMessage       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *GetMessageListResData) Reset() {
	*x = GetMessageListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageListResData) ProtoMessage() {}

func (x *GetMessageListResData) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageListResData.ProtoReflect.Descriptor instead.
func (*GetMessageListResData) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{17}
}

func (x *GetMessageListResData) GetList() []*UserMessage {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetMessageListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 我的消息分类
type UserMessageType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"消息分类ID"`               // 消息分类ID
	Name    string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"分类名字,在我的消息里《标题》"`   // 分类名字,在我的消息里《标题》
	IconUrl string `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty" dc:"分类图标，标题《图标》"` // 分类图标，标题《图标》
}

func (x *UserMessageType) Reset() {
	*x = UserMessageType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserMessageType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserMessageType) ProtoMessage() {}

func (x *UserMessageType) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserMessageType.ProtoReflect.Descriptor instead.
func (*UserMessageType) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{18}
}

func (x *UserMessageType) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserMessageType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserMessageType) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

type UserMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"消息唯一id"`                                                                          // 消息唯一id
	MessageType *UserMessageType       `protobuf:"bytes,3,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty" dc:"消息分类"`                                        // 消息分类
	ContentType UserMessageContentType `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3,enum=message.v1.UserMessageContentType" json:"content_type,omitempty" dc:"1文字"` // 1文字
	Content     []byte                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty" dc:"内容，这字段在json返回是base64格式，根据content_type解码"`                                // 内容，这字段在json返回是base64格式，根据content_type解码
	ReadTime    int64                  `protobuf:"varint,6,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty" dc:"阅读时间，0则标识未读"`                                         // 阅读时间，0则标识未读
	CreatedTime int64                  `protobuf:"varint,7,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty" dc:"创建时间就是发送时间"`                                 // 创建时间就是发送时间
	Title       string                 `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                                                                         // 标题
}

func (x *UserMessage) Reset() {
	*x = UserMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_message_v1_message_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserMessage) ProtoMessage() {}

func (x *UserMessage) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserMessage.ProtoReflect.Descriptor instead.
func (*UserMessage) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{19}
}

func (x *UserMessage) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserMessage) GetMessageType() *UserMessageType {
	if x != nil {
		return x.MessageType
	}
	return nil
}

func (x *UserMessage) GetContentType() UserMessageContentType {
	if x != nil {
		return x.ContentType
	}
	return UserMessageContentType_UNSPECIFIED
}

func (x *UserMessage) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *UserMessage) GetReadTime() int64 {
	if x != nil {
		return x.ReadTime
	}
	return 0
}

func (x *UserMessage) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *UserMessage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

var File_message_v1_message_proto protoreflect.FileDescriptor

var file_message_v1_message_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a, 0x0e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x6c, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xcb, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x69,
	0x73, 0x52, 0x65, 0x61, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x0e, 0x0a, 0x0c, 0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x22,
	0x88, 0x01, 0x0a, 0x0c, 0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2d, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2e, 0x0a, 0x0d, 0x48, 0x61,
	0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x68,
	0x61, 0x73, 0x5f, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x68, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x22, 0x22, 0x0a, 0x10, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x5d,
	0x0a, 0x10, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x1f, 0x0a,
	0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x87,
	0x01, 0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x24, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x5d,
	0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x26, 0x0a,
	0x12, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x5f, 0x0a, 0x12, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x3c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6e, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x4f, 0x0a, 0x0f,
	0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0x94, 0x02,
	0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e, 0x0a,
	0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x2a, 0x33, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x32, 0x85, 0x03, 0x0a, 0x0e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x09,
	0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x48, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x12, 0x42, 0x0a,
	0x0a, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65, 0x12, 0x19, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65, 0x52, 0x65,
	0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x51, 0x0a, 0x0f, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x61, 0x64, 0x12, 0x1e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x42, 0x33, 0x5a, 0x31, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_message_v1_message_proto_rawDescOnce sync.Once
	file_message_v1_message_proto_rawDescData = file_message_v1_message_proto_rawDesc
)

func file_message_v1_message_proto_rawDescGZIP() []byte {
	file_message_v1_message_proto_rawDescOnce.Do(func() {
		file_message_v1_message_proto_rawDescData = protoimpl.X.CompressGZIP(file_message_v1_message_proto_rawDescData)
	})
	return file_message_v1_message_proto_rawDescData
}

var file_message_v1_message_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_message_v1_message_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_message_v1_message_proto_goTypes = []interface{}{
	(UserMessageContentType)(0),   // 0: message.v1.UserMessageContentType
	(*MessageListReq)(nil),        // 1: message.v1.MessageListReq
	(*MessageListRes)(nil),        // 2: message.v1.MessageListRes
	(*MessageListData)(nil),       // 3: message.v1.MessageListData
	(*MessageListItem)(nil),       // 4: message.v1.MessageListItem
	(*HasUnreadReq)(nil),          // 5: message.v1.HasUnreadReq
	(*HasUnreadRes)(nil),          // 6: message.v1.HasUnreadRes
	(*HasUnreadData)(nil),         // 7: message.v1.HasUnreadData
	(*MessageDeleteReq)(nil),      // 8: message.v1.MessageDeleteReq
	(*MessageDeleteRes)(nil),      // 9: message.v1.MessageDeleteRes
	(*MessageOneReq)(nil),         // 10: message.v1.MessageOneReq
	(*MessageOneRes)(nil),         // 11: message.v1.MessageOneRes
	(*DeleteMessageReq)(nil),      // 12: message.v1.DeleteMessageReq
	(*DeleteMessageRes)(nil),      // 13: message.v1.DeleteMessageRes
	(*MarkMessageReadReq)(nil),    // 14: message.v1.MarkMessageReadReq
	(*MarkMessageReadRes)(nil),    // 15: message.v1.MarkMessageReadRes
	(*GetMessageListReq)(nil),     // 16: message.v1.GetMessageListReq
	(*GetMessageListRes)(nil),     // 17: message.v1.GetMessageListRes
	(*GetMessageListResData)(nil), // 18: message.v1.GetMessageListResData
	(*UserMessageType)(nil),       // 19: message.v1.UserMessageType
	(*UserMessage)(nil),           // 20: message.v1.UserMessage
	(*common.PageRequest)(nil),    // 21: common.PageRequest
	(*common.Error)(nil),          // 22: common.Error
	(*common.PageResponse)(nil),   // 23: common.PageResponse
}
var file_message_v1_message_proto_depIdxs = []int32{
	21, // 0: message.v1.MessageListReq.page:type_name -> common.PageRequest
	22, // 1: message.v1.MessageListRes.error:type_name -> common.Error
	3,  // 2: message.v1.MessageListRes.data:type_name -> message.v1.MessageListData
	23, // 3: message.v1.MessageListData.page:type_name -> common.PageResponse
	4,  // 4: message.v1.MessageListData.list:type_name -> message.v1.MessageListItem
	22, // 5: message.v1.HasUnreadRes.error:type_name -> common.Error
	7,  // 6: message.v1.HasUnreadRes.data:type_name -> message.v1.HasUnreadData
	22, // 7: message.v1.MessageDeleteRes.error:type_name -> common.Error
	22, // 8: message.v1.MessageOneRes.error:type_name -> common.Error
	20, // 9: message.v1.MessageOneRes.data:type_name -> message.v1.UserMessage
	22, // 10: message.v1.DeleteMessageRes.error:type_name -> common.Error
	22, // 11: message.v1.MarkMessageReadRes.error:type_name -> common.Error
	21, // 12: message.v1.GetMessageListReq.page:type_name -> common.PageRequest
	22, // 13: message.v1.GetMessageListRes.error:type_name -> common.Error
	18, // 14: message.v1.GetMessageListRes.data:type_name -> message.v1.GetMessageListResData
	20, // 15: message.v1.GetMessageListResData.list:type_name -> message.v1.UserMessage
	23, // 16: message.v1.GetMessageListResData.page:type_name -> common.PageResponse
	19, // 17: message.v1.UserMessage.message_type:type_name -> message.v1.UserMessageType
	0,  // 18: message.v1.UserMessage.content_type:type_name -> message.v1.UserMessageContentType
	5,  // 19: message.v1.MessageService.HasUnread:input_type -> message.v1.HasUnreadReq
	10, // 20: message.v1.MessageService.MessageOne:input_type -> message.v1.MessageOneReq
	16, // 21: message.v1.MessageService.GetMessageList:input_type -> message.v1.GetMessageListReq
	14, // 22: message.v1.MessageService.MarkMessageRead:input_type -> message.v1.MarkMessageReadReq
	12, // 23: message.v1.MessageService.DeleteMessage:input_type -> message.v1.DeleteMessageReq
	6,  // 24: message.v1.MessageService.HasUnread:output_type -> message.v1.HasUnreadRes
	11, // 25: message.v1.MessageService.MessageOne:output_type -> message.v1.MessageOneRes
	17, // 26: message.v1.MessageService.GetMessageList:output_type -> message.v1.GetMessageListRes
	15, // 27: message.v1.MessageService.MarkMessageRead:output_type -> message.v1.MarkMessageReadRes
	13, // 28: message.v1.MessageService.DeleteMessage:output_type -> message.v1.DeleteMessageRes
	24, // [24:29] is the sub-list for method output_type
	19, // [19:24] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_message_v1_message_proto_init() }
func file_message_v1_message_proto_init() {
	if File_message_v1_message_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_message_v1_message_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasUnreadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasUnreadRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasUnreadData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageDeleteRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageOneRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMessageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMessageRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkMessageReadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkMessageReadRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserMessageType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_message_v1_message_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_message_v1_message_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_message_v1_message_proto_goTypes,
		DependencyIndexes: file_message_v1_message_proto_depIdxs,
		EnumInfos:         file_message_v1_message_proto_enumTypes,
		MessageInfos:      file_message_v1_message_proto_msgTypes,
	}.Build()
	File_message_v1_message_proto = out.File
	file_message_v1_message_proto_rawDesc = nil
	file_message_v1_message_proto_goTypes = nil
	file_message_v1_message_proto_depIdxs = nil
}
