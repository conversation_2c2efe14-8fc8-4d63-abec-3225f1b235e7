// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: ws/v1/ws.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MessageEnvelope 是所有 WebSocket 通信的顶级消息信封
// 通过将消息类型分为 C2S (Client to Server) 和 S2C (Server to Client)，用于区分消息是由谁发出的
type MessageEnvelope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payload:
	//
	//	*MessageEnvelope_C2SAuthMessage
	//	*MessageEnvelope_S2CSystemNotification
	Payload isMessageEnvelope_Payload `protobuf_oneof:"payload" dc:"*MessageEnvelope_C2SAuthMessage*MessageEnvelope_S2CSystemNotification"`
}

func (x *MessageEnvelope) Reset() {
	*x = MessageEnvelope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ws_v1_ws_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageEnvelope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageEnvelope) ProtoMessage() {}

func (x *MessageEnvelope) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageEnvelope.ProtoReflect.Descriptor instead.
func (*MessageEnvelope) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{0}
}

func (m *MessageEnvelope) GetPayload() isMessageEnvelope_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *MessageEnvelope) GetC2SAuthMessage() *C2SAuthMessage {
	if x, ok := x.GetPayload().(*MessageEnvelope_C2SAuthMessage); ok {
		return x.C2SAuthMessage
	}
	return nil
}

func (x *MessageEnvelope) GetS2CSystemNotification() *S2CSystemNotification {
	if x, ok := x.GetPayload().(*MessageEnvelope_S2CSystemNotification); ok {
		return x.S2CSystemNotification
	}
	return nil
}

type isMessageEnvelope_Payload interface {
	isMessageEnvelope_Payload()
}

type MessageEnvelope_C2SAuthMessage struct {
	// 客户端 -> 服务器 消息
	// 将 C2S 消息类型从 1 开始
	// 所有认证相关的消息放在 1-10 之间
	C2SAuthMessage *C2SAuthMessage `protobuf:"bytes,1,opt,name=c2s_auth_message,json=c2sAuthMessage,proto3,oneof" dc:"客户端 -> 服务器 消息将 C2S 消息类型从 1 开始所有认证相关的消息放在 1-10 之间发送登录信息"` // 发送登录信息
}

type MessageEnvelope_S2CSystemNotification struct {
	// 服务器 -> 客户端 消息
	// S2C 消息类型从 1001 开始
	S2CSystemNotification *S2CSystemNotification `protobuf:"bytes,1000,opt,name=s2c_system_notification,json=s2cSystemNotification,proto3,oneof" dc:"服务器 -> 客户端 消息S2C 消息类型从 1001 开始服务器发送的系统通知"` // 服务器发送的系统通知
}

func (*MessageEnvelope_C2SAuthMessage) isMessageEnvelope_Payload() {}

func (*MessageEnvelope_S2CSystemNotification) isMessageEnvelope_Payload() {}

// 客户端发送给服务器的聊天消息
type C2SAuthMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token     string            `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录返回的jwt token"`                     // 登录返回的jwt token
	UserId    uint64            `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" dc:"用户ID"`              // 用户ID
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty" dc:"FrontInfo"` // FrontInfo
}

func (x *C2SAuthMessage) Reset() {
	*x = C2SAuthMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ws_v1_ws_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2SAuthMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAuthMessage) ProtoMessage() {}

func (x *C2SAuthMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAuthMessage.ProtoReflect.Descriptor instead.
func (*C2SAuthMessage) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{1}
}

func (x *C2SAuthMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2SAuthMessage) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *C2SAuthMessage) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

// 服务器发送给客户端的系统通知
type S2CAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *S2CAck) Reset() {
	*x = S2CAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ws_v1_ws_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2CAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAck) ProtoMessage() {}

func (x *S2CAck) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAck.ProtoReflect.Descriptor instead.
func (*S2CAck) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{2}
}

type S2CSystemNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title     string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message   string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp uint64 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *S2CSystemNotification) Reset() {
	*x = S2CSystemNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ws_v1_ws_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2CSystemNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CSystemNotification) ProtoMessage() {}

func (x *S2CSystemNotification) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CSystemNotification.ProtoReflect.Descriptor instead.
func (*S2CSystemNotification) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{3}
}

func (x *S2CSystemNotification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *S2CSystemNotification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *S2CSystemNotification) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_ws_v1_ws_proto protoreflect.FileDescriptor

var file_ws_v1_ws_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45,
	0x6e, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x10, 0x63, 0x32, 0x73, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x32, 0x53, 0x41, 0x75, 0x74,
	0x68, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x32, 0x73, 0x41,
	0x75, 0x74, 0x68, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x17, 0x73, 0x32,
	0x63, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0xe8, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x77,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x32, 0x43, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x15, 0x73, 0x32,
	0x63, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x71,
	0x0a, 0x0e, 0x43, 0x32, 0x53, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x08, 0x0a, 0x06, 0x53, 0x32, 0x43, 0x41, 0x63, 0x6b, 0x22, 0x65, 0x0a, 0x15, 0x53,
	0x32, 0x43, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x24, 0x5a, 0x22, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ws_v1_ws_proto_rawDescOnce sync.Once
	file_ws_v1_ws_proto_rawDescData = file_ws_v1_ws_proto_rawDesc
)

func file_ws_v1_ws_proto_rawDescGZIP() []byte {
	file_ws_v1_ws_proto_rawDescOnce.Do(func() {
		file_ws_v1_ws_proto_rawDescData = protoimpl.X.CompressGZIP(file_ws_v1_ws_proto_rawDescData)
	})
	return file_ws_v1_ws_proto_rawDescData
}

var file_ws_v1_ws_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_ws_v1_ws_proto_goTypes = []interface{}{
	(*MessageEnvelope)(nil),       // 0: ws.v1.MessageEnvelope
	(*C2SAuthMessage)(nil),        // 1: ws.v1.C2SAuthMessage
	(*S2CAck)(nil),                // 2: ws.v1.S2CAck
	(*S2CSystemNotification)(nil), // 3: ws.v1.S2CSystemNotification
	(*common.FrontInfo)(nil),      // 4: common.FrontInfo
}
var file_ws_v1_ws_proto_depIdxs = []int32{
	1, // 0: ws.v1.MessageEnvelope.c2s_auth_message:type_name -> ws.v1.C2SAuthMessage
	3, // 1: ws.v1.MessageEnvelope.s2c_system_notification:type_name -> ws.v1.S2CSystemNotification
	4, // 2: ws.v1.C2SAuthMessage.front_info:type_name -> common.FrontInfo
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_ws_v1_ws_proto_init() }
func file_ws_v1_ws_proto_init() {
	if File_ws_v1_ws_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ws_v1_ws_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageEnvelope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ws_v1_ws_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2SAuthMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ws_v1_ws_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2CAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ws_v1_ws_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2CSystemNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_ws_v1_ws_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*MessageEnvelope_C2SAuthMessage)(nil),
		(*MessageEnvelope_S2CSystemNotification)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ws_v1_ws_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ws_v1_ws_proto_goTypes,
		DependencyIndexes: file_ws_v1_ws_proto_depIdxs,
		MessageInfos:      file_ws_v1_ws_proto_msgTypes,
	}.Build()
	File_ws_v1_ws_proto = out.File
	file_ws_v1_ws_proto_rawDesc = nil
	file_ws_v1_ws_proto_goTypes = nil
	file_ws_v1_ws_proto_depIdxs = nil
}
