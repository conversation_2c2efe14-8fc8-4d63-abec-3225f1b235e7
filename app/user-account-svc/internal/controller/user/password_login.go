package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
)

// 密码登录（邮箱）
func (*Controller) SignIn(ctx context.Context, req *v1.SignInReq) (res *v1.UserSignInRes, err error) {
	var (
		token     string
		userModel *model.User
	)
	frontInfoModel := &model.FrontInfo{}
	gconv.Scan(req.FrontInfo, frontInfoModel)
	if req.SignInType == v1.SignInType_SIGN_IN_TYPE_EMAIL {
		if !utility.IsValidEmail(req.Account) {
			return nil, errno.T(ctx, errno.CodeInvalidEmailError)
		}
		// 邮箱密码登录
		userModel, token, err = service.User().SignInByEmailPass(ctx, req.Account, req.Password, frontInfoModel)
	}
	if err != nil {
		return nil, err
	}

	g.Log().Debug(ctx, userModel)
	res = &v1.UserSignInRes{
		Code: 200,
		Msg:  "登录成功",
		Data: &v1.UserSignInResData{
			Token:     token,
			SessionId: userModel.SessionId,
			Secret:    userModel.SecretKey,
			UserInfo: &v1.UserInfo{
				Id:           userModel.Id,
				BindPhone:    true,
				BindRealName: false, // 实名认证
				Gender:       v1.Gender(gconv.Int32(userModel.Gender)),
				Avatar:       userModel.Avatar,
				Nickname:     userModel.Nickname,
				PhoneNum:     userModel.PhoneNum,
				AreaCode:     userModel.AreaCode,
				FirstName:    userModel.FirstName,
				MiddleName:   userModel.MiddleName,
				LastName:     userModel.LastName,
			},
		},
	}

	return res, nil
}
