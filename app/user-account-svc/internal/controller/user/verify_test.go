package user

import (
	"github.com/gogf/gf/v2/test/gtest"
	"halalplus/api/common"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"testing"
)

func Test_SendVerifyCode(t *testing.T) {
	mp := &common.PhoneInfo{
		AreaCode: "+62",
		PhoneNum: "**********",
	}

	// 修改电话号码第二步,发送验证码
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SendVerifyCode(reqCtx, &userv1.SendVerifyCodeReq{
			PhoneInfo:         mp,
			VerifyCodeChannel: userv1.VerifyCodeChannel_SMS,
			VerifyCodeScene:   userv1.VerifyCodeScene_BIND_PHONE,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
}

func Test_SendVerifyCode_EMAIL(t *testing.T) {
	// 发送邮箱验证码（登录/注册）
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SendVerifyCode(reqCtx, &userv1.SendVerifyCodeReq{
			Email:             "<EMAIL>",
			VerifyCodeChannel: userv1.VerifyCodeChannel_EMAIL,
			VerifyCodeScene:   userv1.VerifyCodeScene_LOGIN,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
}

func Test_SendVerifyCode_MY_EMAIL(t *testing.T) {
	// 发送验证码“我的邮箱”
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SendVerifyCode(reqCtx, &userv1.SendVerifyCodeReq{
			Email:             "231@.com",
			VerifyCodeChannel: userv1.VerifyCodeChannel_EMAIL,
			VerifyCodeScene:   userv1.VerifyCodeScene_MY_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
	// 校验
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.VerifyCode(reqCtx, &userv1.VerifyCodeReq{
			OptCode:         "123456",
			VerifyCodeScene: userv1.VerifyCodeScene_MY_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
}
