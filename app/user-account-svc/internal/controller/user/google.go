package user

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/golang-jwt/jwt/v5"
	v1 "halalplus/app/user-account-svc/api/user/v1"
)

// 配置Google 登录，使用 OAuth 2.0 (Google People API ）
//
//	无论是 Google 还是 Apple，流程都类似：
//		App 端 调用 Google / Apple SDK，拿到 id_token。
//		App → 你的 GoFrame 服务：带上 id_token 调用 /login/google 或 /login/apple。
//	GoFrame 后端：
//		校验 id_token 真伪（Google 用 google-auth-library 机制，Apple 用 JWT 验签）。
//		解析用户唯一 ID（Google 的 sub，Apple 的 sub）。

func (*Controller) LoginGoogle(ctx context.Context, req *v1.LoginGoogleReq) (res *v1.LoginGoogleRes, err error) {

	verifyGoogleIDToken(ctx, req.IdToken, g.Cfg().MustGet(ctx, "google.auth.clientID", "xxxxxx").String())
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

// Google 的公钥获取地址
const googleCertsURL = "https://www.googleapis.com/oauth2/v3/certs"

// 验证 Google ID Token
func verifyGoogleIDToken(ctx context.Context, idToken, clientID string) (map[string]interface{}, error) {
	// 1. 解析 JWT header，找到 kid
	token, _ := jwt.Parse(idToken, nil)
	if token == nil {
		return nil, fmt.Errorf("invalid token")
	}
	kid := token.Header["kid"].(string)

	// 2. 下载 Google 公钥
	body := g.Client().GetContent(ctx, googleCertsURL)
	g.Log().Debug(ctx, "body:", body)
	// 调用 Google 官方接口验证：
	data := g.Client().GetContent(ctx, "https://oauth2.googleapis.com/tokeninfo?id_token="+idToken)

	// data 就是 JSON，包含 aud, exp, sub, email 等
	// 需要检查 aud == clientID
	return map[string]interface{}{"raw": string(data), "kid": kid}, nil
}
