package user

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
)

// 电话号码+短信验证码登录（自动注册）
func (*Controller) SignInByPhone(ctx context.Context, req *v1.SignInByPhoneReq) (res *v1.SignInByPhoneRes, err error) {
	if req.PhoneInfo == nil {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter)
	}

	// TODO：检查手机号码有效性
	// TODO： 设备号+手机号，风控检查
	mp := &model.PhoneInfo{
		AreaCode: req.PhoneInfo.AreaCode,
		PhoneNum: req.PhoneInfo.PhoneNum,
	}
	if !mp.IsValid() {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter)
	}
	// 验证手机号验证码 是否匹配
	ok, err := service.Verify().VerifyLoginCode(ctx, mp, req.OptCode)
	if err != nil || !ok {
		return nil, gerror.NewCode(errno.CodeOptCodeError)
	}

	frontInfoModel := &model.FrontInfo{}
	gconv.Scan(req.FrontInfo, frontInfoModel)
	userModel, token, err := service.User().SignInByPhoneNum(ctx, mp.GetAreaCode(), mp.GetPhoneNum(), frontInfoModel)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	g.Log().Debug(ctx, userModel)
	res = &v1.SignInByPhoneRes{
		Code: 200,
		Msg:  "登录成功",
		Data: &v1.SignInByPhoneResData{
			Token:     token,
			SessionId: userModel.SessionId,
			Secret:    userModel.SecretKey,
			UserInfo: &v1.UserInfo{
				Id:           userModel.Id,
				BindPhone:    true,
				BindRealName: false, // 实名认证
				Gender:       v1.Gender(gconv.Int32(userModel.Gender)),
				Avatar:       userModel.Avatar,
				Nickname:     userModel.Nickname,
				PhoneNum:     userModel.PhoneNum,
				AreaCode:     userModel.AreaCode,
				FirstName:    userModel.FirstName,
				MiddleName:   userModel.MiddleName,
				LastName:     userModel.LastName,
			},
		},
	}

	return res, nil
}

func (*Controller) PhoneValidCheck(ctx context.Context, req *v1.PhoneValidCheckReq) (res *v1.PhoneValidCheckRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

// ChangePhone 更换登录手机号码
func (*Controller) ChangePhone(ctx context.Context, req *v1.ChangePhoneReq) (res *v1.ChangePhoneRes, err error) {
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	mp := &model.PhoneInfo{
		AreaCode: req.PhoneInfo.AreaCode,
		PhoneNum: req.PhoneInfo.PhoneNum,
	}
	if !mp.IsValid() {
		g.Log().Debug(ctx, "phone is invalid")
		return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
	}

	// 检查验证码
	ok, err := service.Verify().VerifyBindPhoneCode(ctx, mp, req.OptCode)
	if err != nil || !ok {
		g.Log().Debug(ctx, "检查验证码失败", err)
		return nil, err
	}

	err = service.User().ChangePhoneNumber(ctx, uid, mp)
	if err != nil {
		g.Log().Error(ctx, "ChangePhoneNumber", err)
		return nil, err
	}
	return &v1.ChangePhoneRes{
		Code: 200,
		Msg:  "success",
	}, nil
}
