package user

import (
	"github.com/gogf/gf/v2/test/gtest"
	"halalplus/api/common"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"testing"
)

func TestController_PhoneValidCheck(t *testing.T) {
	// 电话号码校验
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.PhoneValidCheck(ctx, &userv1.PhoneValidCheckReq{
			PhoneInfo: &common.PhoneInfo{
				AreaCode: "+62",
				PhoneNum: "**********",
			},
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "15000")
	})
}

func Test_SignInByPhone(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 登录场景发送短信验证码
		otpRes, err := userServiceClient.SendVerifyCode(ctx, &userv1.SendVerifyCodeReq{
			PhoneInfo: &common.PhoneInfo{
				PhoneNum: "**********",
				AreaCode: "+62",
			},
			VerifyCodeChannel: userv1.VerifyCodeChannel_SMS,
			VerifyCodeScene:   userv1.VerifyCodeScene_LOGIN,
		})
		t.Assert(err, nil)
		t.Log(otpRes)
		t.Assert(otpRes.Code, "200")

		// 电话短信登录
		res, err := userServiceClient.SignInByPhone(ctx, &userv1.SignInByPhoneReq{
			OptCode: "123456",
			PhoneInfo: &common.PhoneInfo{
				AreaCode: "+62",
				PhoneNum: "**********",
			},
			FrontInfo: frontInfo,
		})
		t.Assert(err, nil)
		t.Log(res)
		t.Assert(res.Code, "200")
	})
}

func Test_ChangePhone(t *testing.T) {
	mp := &common.PhoneInfo{
		AreaCode: "+62",
		PhoneNum: "866222224",
	}

	// 修改电话号码第二步,发送验证码
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SendVerifyCode(reqCtx, &userv1.SendVerifyCodeReq{
			PhoneInfo:         mp,
			VerifyCodeChannel: userv1.VerifyCodeChannel_SMS,
			VerifyCodeScene:   userv1.VerifyCodeScene_BIND_PHONE,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})

	// 修改电话号码
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.ChangePhone(reqCtx, &userv1.ChangePhoneReq{
			PhoneInfo: mp,
			OptCode:   "123456",
			FrontInfo: frontInfo,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
}
