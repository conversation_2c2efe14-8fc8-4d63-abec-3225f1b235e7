package user

import (
	"github.com/gogf/gf/v2/test/gtest"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"testing"
)

func TestController_SetPassword(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SetPassword(reqCtx, &userv1.SetPasswordReq{
			Password: "123456",
		})
		t.Log(res)
		t.<PERSON>(err, nil)
		t.<PERSON>(res.Code, "200")
	})
}

func TestController_ChangePassword(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.ChangePassword(reqCtx, &userv1.ChangePasswordReq{
			NewPassword: "123456",
			OldPassword: "123456",
		})
		t.Log(res)
		t.<PERSON>ser<PERSON>(err, nil)
		t.<PERSON>(res.Code, "200")
	})
}
