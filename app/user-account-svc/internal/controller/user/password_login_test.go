package user

import (
	"github.com/gogf/gf/v2/test/gtest"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"testing"
)

func TestController_SignIn(t *testing.T) {
	// 邮箱密码登录测试 用户不存在15000
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignIn(ctx, &userv1.SignInReq{
			Account:    "<EMAIL>",
			Password:   "123456",
			FrontInfo:  frontInfo,
			SignInType: userv1.SignInType_SIGN_IN_TYPE_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "15000")
	})
	// 邮箱密码登录测试 密码错误15015
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignIn(ctx, &userv1.SignInReq{
			Account:    "<EMAIL>",
			Password:   "123456",
			FrontInfo:  frontInfo,
			SignInType: userv1.SignInType_SIGN_IN_TYPE_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "15015")
	})
	// 邮箱密码登录测试 登录成功
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignIn(ctx, &userv1.SignInReq{
			Account:    "<EMAIL>",
			Password:   "********",
			FrontInfo:  frontInfo,
			SignInType: userv1.SignInType_SIGN_IN_TYPE_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
	// 邮箱密码登录测试 email格式不对15016
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignIn(ctx, &userv1.SignInReq{
			Account:    "**********@.com",
			Password:   "********",
			FrontInfo:  frontInfo,
			SignInType: userv1.SignInType_SIGN_IN_TYPE_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "15016")
	})
}
