package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/service"
)

// 首次设置密码（或从无密码态补设）
func (*Controller) SetPassword(ctx context.Context, req *v1.SetPasswordReq) (res *v1.SetPasswordRes, err error) {
	// 密码格式检查
	if !service.Utility().IsValidPassword(req.Password) {
		return nil, errno.T(ctx, errno.CodeUserPasswordError)
	}

	// 登录态
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil || uid <= 0 {
		return nil, err
	}

	usr, err := dao.User.GetUserEntityById(ctx, uid)
	if err != nil {
		// 用户不存在
		return nil, errno.T(ctx, errno.CodeUserNotFoundError)
	}
	if !g.IsEmpty(usr.Password) {
		// 已设置密码
		return nil, errno.T(ctx, errno.CodeUserPasswordExisted)
	}
	hashPassword, _ := service.Utility().HashPassword(req.Password)
	err = dao.User.SetPassword(ctx, uid, hashPassword)
	if err != nil {
		return nil, err
	}
	return &v1.SetPasswordRes{
		Code: 200,
	}, nil
}

// 已登录用户用旧密码修改
func (*Controller) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error) {
	// 密码格式检查
	if !service.Utility().IsValidPassword(req.NewPassword) {
		return nil, errno.T(ctx, errno.CodeUserPasswordError)
	}

	// 登录态
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil || uid <= 0 {
		return nil, err
	}

	usr, err := dao.User.GetUserEntityById(ctx, uid)
	if err != nil {
		// 用户不存在
		return nil, errno.T(ctx, errno.CodeUserNotFoundError)
	}
	validPass := service.Utility().CheckPasswordHash(req.OldPassword, usr.Password)
	if !validPass {
		// 密码错误
		return nil, errno.T(ctx, errno.CodeUserPasswordInvalid)
	}
	hashPassword, _ := service.Utility().HashPassword(req.NewPassword)
	err = dao.User.SetPassword(ctx, uid, hashPassword)
	if err != nil {
		return nil, err
	}
	return &v1.ChangePasswordRes{
		Code: 200,
	}, nil
}

// 重置密码
func (*Controller) ResetPassword(ctx context.Context, req *v1.ResetPasswordReq) (res *v1.ResetPasswordRes, err error) {
	// 密码格式检查
	if !service.Utility().IsValidPassword(req.NewPassword) {
		return nil, errno.T(ctx, errno.CodeUserPasswordError)
	}

	// 参数不正确
	if g.IsEmpty(req.ResetToken) {
		return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
	}

	// 获取用户信息
	rt, err := service.Verify().GetResetToken(ctx, req.ResetToken)
	if err != nil {
		return nil, err
	}

	// 重置密码
	hashPassword, _ := service.Utility().HashPassword(req.NewPassword)
	err = dao.User.SetPassword(ctx, rt.UserId, hashPassword)
	if err != nil {
		return nil, err
	}
	return &v1.ResetPasswordRes{
		Code: 200,
	}, nil
}
