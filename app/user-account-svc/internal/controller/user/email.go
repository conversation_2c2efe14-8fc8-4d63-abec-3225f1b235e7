package user

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
	"strings"
)

// 检查邮箱是否被注册
func (*Controller) EmailValidCheck(ctx context.Context, req *v1.EmailValidCheckReq) (res *v1.EmailValidCheckRes, err error) {
	if g.IsEmpty(req.Email) {
		return nil, errno.T(ctx, gcode.CodeInvalidParameter)
	}

	// 邮箱格式检查
	req.Email = strings.TrimSpace(req.Email)
	if !utility.IsValidEmail(req.Email) {
		return nil, errno.T(ctx, errno.CodeInvalidEmailError)
	}

	// 查询用户登录邮箱
	count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Email, req.Email).Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		// 邮箱已注册
		return nil, errno.T(ctx, errno.CodeUserEmailExisted)
	}

	// 未注册邮箱，不报错
	return &v1.EmailValidCheckRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

// 邮箱验证码登录（自动注册）
func (*Controller) SignInByEmail(ctx context.Context, req *v1.SignInByEmailReq) (res *v1.SignInByEmailRes, err error) {
	// 合法邮箱
	email := strings.TrimSpace(req.Email)
	if !utility.IsValidEmail(email) {
		return nil, errno.T(ctx, errno.CodeInvalidEmailError)
	}

	// 验证码校验
	ok, err := service.Verify().VerifyEmailLoginCode(ctx, email, req.OptCode)
	if err != nil || !ok {
		return nil, gerror.NewCode(errno.CodeOptCodeError)
	}

	frontInfoModel := &model.FrontInfo{}
	gconv.Scan(req.FrontInfo, frontInfoModel)
	userModel, token, err := service.User().SignInByEmail(ctx, email, frontInfoModel)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	g.Log().Debug(ctx, userModel)
	res = &v1.SignInByEmailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.SignInByEmailResData{
			Token:     token,
			SessionId: userModel.SessionId,
			Secret:    userModel.SecretKey,
			UserInfo: &v1.UserInfo{
				Id:           userModel.Id,
				BindPhone:    !g.IsEmpty(userModel.PhoneNum),
				BindRealName: !g.IsEmpty(userModel.FirstName), // 实名认证
				BindEmail:    !g.IsEmpty(userModel.Email),
				Gender:       v1.Gender(gconv.Int32(userModel.Gender)),
				Avatar:       userModel.Avatar,
				Nickname:     userModel.Nickname,
				PhoneNum:     userModel.PhoneNum,
				AreaCode:     userModel.AreaCode,
				FirstName:    userModel.FirstName,
				MiddleName:   userModel.MiddleName,
				LastName:     userModel.LastName,
			},
		},
	}
	return res, nil
}

// 修改登录邮箱
func (*Controller) ChangeEmail(ctx context.Context, req *v1.ChangeEmailReq) (res *v1.ChangeEmailRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
