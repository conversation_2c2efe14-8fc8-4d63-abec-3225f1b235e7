package verify

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/test/gtest"
	"halalplus/app/user-account-svc/internal/boot"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"math/rand"
	"testing"
)

func TestMain(m *testing.M) {
	//os.Setenv("GF_GCFG_PATH", "../../../manifest/config/config.yaml")
	g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetPath("../../../")
	boot.Init()
	g.Log().Info(context.Background(), "TestMain")
	m.Run()
}

func TestSVerify_GetResetToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		uid := rand.Uint64()
		token, err := service.Verify().SetResetToken(t.Context(), &model.ResetToken{
			UserId: uid,
		})
		t.Log(token, err)
		rt, _ := service.Verify().GetResetToken(t.Context(), token)
		t.Assert(rt.UserId, uid)
		t.Log(rt)

		rt, err = service.Verify().GetResetToken(t.Context(), token)
		t.Log(token, err)
		t.Assert(err, nil)
		t.Assert(rt.UserId, 0)
		t.Log(rt)
	})

}
