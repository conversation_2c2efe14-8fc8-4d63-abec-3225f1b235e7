package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"halalplus/app/user-account-svc/internal/boot"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/utility/bloom"
	"testing"
)

func TestMain(m *testing.M) {
	//os.Setenv("GF_GCFG_PATH", "../../../manifest/config/config.yaml")
	g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetPath("../../../")
	boot.Init()
	g.Log().Info(context.Background(), "TestMain")
	m.Run()
}

func Test_sUser_ChangePhoneNumber(t *testing.T) {
	type fields struct {
		signInMsgChan        chan *model.SignInLogInput
		attrsBatchUpdate<PERSON>han chan *model.AttrsToUpdate
		attrsNoDelay<PERSON>han     chan *model.AttrsToUpdate
		quit                 chan struct{}
		ConfigCaptcha        string
		signInRecordChan     chan *do.UserSigninLog
		accountSet           *bloom.Filter
		transferSet          *bloom.Filter
		phoneSet             *bloom.Filter
	}
	type args struct {
		ctx context.Context
		uid uint64
		mp  *model.PhoneInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "changePhoneNumber",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				uid: 73,
				mp: &model.PhoneInfo{
					AreaCode: "+62",
					PhoneNum: "**********",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sUser{
				signInMsgChan:        tt.fields.signInMsgChan,
				attrsBatchUpdateChan: tt.fields.attrsBatchUpdateChan,
				attrsNoDelayChan:     tt.fields.attrsNoDelayChan,
				quit:                 tt.fields.quit,
				ConfigCaptcha:        tt.fields.ConfigCaptcha,
				signInRecordChan:     tt.fields.signInRecordChan,
				accountSet:           tt.fields.accountSet,
				transferSet:          tt.fields.transferSet,
				phoneSet:             tt.fields.phoneSet,
			}
			if err := s.ChangePhoneNumber(tt.args.ctx, tt.args.uid, tt.args.mp); (err != nil) != tt.wantErr {
				t.Errorf("ChangePhoneNumber() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
