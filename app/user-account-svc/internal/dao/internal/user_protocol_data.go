// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserProtocolDataDao is the data access object for the table user_protocol_data.
type UserProtocolDataDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  UserProtocolDataColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// UserProtocolDataColumns defines and stores column names for the table user_protocol_data.
type UserProtocolDataColumns struct {
	Id         string //
	TypeId     string // 协议类型 0：用户协议 1：隐私政策
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Content    string // 内容
}

// userProtocolDataColumns holds the columns for the table user_protocol_data.
var userProtocolDataColumns = UserProtocolDataColumns{
	Id:         "id",
	TypeId:     "type_id",
	LanguageId: "language_id",
	Content:    "content",
}

// NewUserProtocolDataDao creates and returns a new DAO object for table data access.
func NewUserProtocolDataDao(handlers ...gdb.ModelHandler) *UserProtocolDataDao {
	return &UserProtocolDataDao{
		group:    "default",
		table:    "user_protocol_data",
		columns:  userProtocolDataColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserProtocolDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserProtocolDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserProtocolDataDao) Columns() UserProtocolDataColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserProtocolDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserProtocolDataDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserProtocolDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
