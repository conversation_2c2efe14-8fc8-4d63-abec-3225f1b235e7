// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserNuAuditDao is the data access object for the table user_nu_audit.
type UserNuAuditDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserNuAuditColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserNuAuditColumns defines and stores column names for the table user_nu_audit.
type UserNuAuditColumns struct {
	Id               string //
	UserNuId         string // nu认证表id
	AuditStatus      string // 认证状态：1待审核 2已通过 3已驳回
	AuditAccount     string // 审核人账号
	AuditAccountName string // 审核人名称
	AuditReason      string // 审核原因
	CreateTime       string // 创建时间(毫秒时间戳)
	UpdateTime       string // 更新时间(毫秒时间戳)
}

// userNuAuditColumns holds the columns for the table user_nu_audit.
var userNuAuditColumns = UserNuAuditColumns{
	Id:               "id",
	UserNuId:         "user_nu_id",
	AuditStatus:      "audit_status",
	AuditAccount:     "audit_account",
	AuditAccountName: "audit_account_name",
	AuditReason:      "audit_reason",
	CreateTime:       "create_time",
	UpdateTime:       "update_time",
}

// NewUserNuAuditDao creates and returns a new DAO object for table data access.
func NewUserNuAuditDao(handlers ...gdb.ModelHandler) *UserNuAuditDao {
	return &UserNuAuditDao{
		group:    "default",
		table:    "user_nu_audit",
		columns:  userNuAuditColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserNuAuditDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserNuAuditDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserNuAuditDao) Columns() UserNuAuditColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserNuAuditDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserNuAuditDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserNuAuditDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
