// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserSafetyConfigDao is the data access object for the table user_safety_config.
type UserSafetyConfigDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  UserSafetyConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// UserSafetyConfigColumns defines and stores column names for the table user_safety_config.
type UserSafetyConfigColumns struct {
	Id          string //
	ConfigType  string // 配置类型 0：短信及WhatsAPP验证码机制 1：其他安全机制
	ConfigKey   string // 配置键名
	ConfigValue string // 配置值
	Description string // 配置描述
	CreateTime  string // 创建时间
	UpdateTime  string // 更新时间
}

// userSafetyConfigColumns holds the columns for the table user_safety_config.
var userSafetyConfigColumns = UserSafetyConfigColumns{
	Id:          "id",
	ConfigType:  "config_type",
	ConfigKey:   "config_key",
	ConfigValue: "config_value",
	Description: "description",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewUserSafetyConfigDao creates and returns a new DAO object for table data access.
func NewUserSafetyConfigDao(handlers ...gdb.ModelHandler) *UserSafetyConfigDao {
	return &UserSafetyConfigDao{
		group:    "default",
		table:    "user_safety_config",
		columns:  userSafetyConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserSafetyConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserSafetyConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserSafetyConfigDao) Columns() UserSafetyConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserSafetyConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserSafetyConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserSafetyConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
