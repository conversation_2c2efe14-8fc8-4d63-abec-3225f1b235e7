// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userProtocolDao is the data access object for the table user_protocol.
// You can define custom methods on it to extend its functionality as needed.
type userProtocolDao struct {
	*internal.UserProtocolDao
}

var (
	// UserProtocol is a globally accessible object for table user_protocol operations.
	UserProtocol = userProtocolDao{internal.NewUserProtocolDao()}
)

// Add your custom methods and functionality below.
