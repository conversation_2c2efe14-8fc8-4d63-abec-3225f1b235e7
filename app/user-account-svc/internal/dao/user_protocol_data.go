// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userProtocolDataDao is the data access object for the table user_protocol_data.
// You can define custom methods on it to extend its functionality as needed.
type userProtocolDataDao struct {
	*internal.UserProtocolDataDao
}

var (
	// UserProtocolData is a globally accessible object for table user_protocol_data operations.
	UserProtocolData = userProtocolDataDao{internal.NewUserProtocolDataDao()}
)

// Add your custom methods and functionality below.
