// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userSafetyConfigDao is the data access object for the table user_safety_config.
// You can define custom methods on it to extend its functionality as needed.
type userSafetyConfigDao struct {
	*internal.UserSafetyConfigDao
}

var (
	// UserSafetyConfig is a globally accessible object for table user_safety_config operations.
	UserSafetyConfig = userSafetyConfigDao{internal.NewUserSafetyConfigDao()}
)

// Add your custom methods and functionality below.
