// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserSafetyConfig is the golang structure of table user_safety_config for DAO operations like Where/Data.
type UserSafetyConfig struct {
	g.Meta      `orm:"table:user_safety_config, do:true"`
	Id          interface{} //
	ConfigType  interface{} // 配置类型 0：短信及WhatsAPP验证码机制 1：其他安全机制
	ConfigKey   interface{} // 配置键名
	ConfigValue interface{} // 配置值
	Description interface{} // 配置描述
	CreateTime  interface{} // 创建时间
	UpdateTime  interface{} // 更新时间
}
