// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserSafetyConfig is the golang structure for table user_safety_config.
type UserSafetyConfig struct {
	Id          uint   `json:"id"          orm:"id"           description:""`                                 //
	ConfigType  int    `json:"configType"  orm:"config_type"  description:"配置类型 0：短信及WhatsAPP验证码机制 1：其他安全机制"` // 配置类型 0：短信及WhatsAPP验证码机制 1：其他安全机制
	ConfigKey   string `json:"configKey"   orm:"config_key"   description:"配置键名"`                             // 配置键名
	ConfigValue string `json:"configValue" orm:"config_value" description:"配置值"`                              // 配置值
	Description string `json:"description" orm:"description"  description:"配置描述"`                             // 配置描述
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`                             // 创建时间
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"更新时间"`                             // 更新时间
}
