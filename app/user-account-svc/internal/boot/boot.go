package boot

import (
	"fmt"
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

func Init() {
	ctx := gctx.New()

	// 获取 Redis 客户端（会自动读取 `config.yaml` 配置）
	client := g.Redis()

	// 测试 Redis 连接
	_, err := client.Do(ctx, "PING")
	if err != nil {
		println("❌ Redis 连接失败:", err.Error())
	} else {
		println("✅ Redis 连接成功！")
	}

	// 获取数据库实例
	db := g.DB()
	if db == nil {
		fmt.Println("❌ 数据库实例获取失败")
		return
	}
}
