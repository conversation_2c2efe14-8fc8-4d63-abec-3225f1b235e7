server:
  id: "2" # 机器id，目前用于雪花算法

# GRPC Server.
grpc:
  address: ":9200"
#  name: "user-account-svc"
  logPath: ""
  logStdout: false
  errorStack: true
  errorLogEnabled: true
  errorLogPattern: "error-{Ymd}.log"
  accessLogEnabled: true
  accessLogPattern: "access-{Ymd}.log"

ipdb:  # ip数据库的路径配置，需要按线上实际的路径进行配置，更新ip数据库文件后需要重启应用程序
  ipv4: ./resource/ipdb/IP2LOCATION-LITE-DB3.BIN
  ipv6: ./resource/ipdb/IP2LOCATION-LITE-DB3.IPV6.BIN

# https://goframe.org/docs/core/glog-config
logger:
  level : "all"
  stdout: true

# https://goframe.org/docs/core/gdb-config-file
database:
  default:
    link: "mysql:admin:Ru5.KcTC%GT*xD~]+r+bGi6bjF@tcp(localhost:3406)/user_account_svc"
    role: "master"
    debug: true

redis:
  default:
    address: "127.0.0.1:6379"
    db: 2
    pass: "INKA#*iidredis"
    cluster: false
    tls: false
    #maxIdle: 200
    #idleTimeout: 20s
    #maxConnLifetime: 60s
