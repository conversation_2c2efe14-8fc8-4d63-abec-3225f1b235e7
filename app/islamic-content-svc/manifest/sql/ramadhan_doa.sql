-- 斋月祷文表
DROP TABLE IF EXISTS `ramadhan_doa`;
CREATE TABLE `ramadhan_doa` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `doa_no` INT NOT NULL COMMENT '祈祷文序号',
    `doa_name` VARCHAR(100) NOT NULL COMMENT '祈祷文名称',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_doa_no` (`doa_no`) COMMENT '祈祷文序号唯一索引',
    INDEX `idx_doa_name` (`doa_name`) COMMENT '祈祷文名称索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='斋月祷文表';

-- 斋月祷文内容表
DROP TABLE IF EXISTS `ramadhan_doa_content`;
CREATE TABLE `ramadhan_doa_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `doa_id` BIGINT UNSIGNED NOT NULL COMMENT '祈祷文ID',
    `content_order` INT NOT NULL DEFAULT 1 COMMENT '内容排序',
    `title` VARCHAR(255) COMMENT '内容标题（可为空）',
    `muqatta_at` VARCHAR(255) COMMENT 'Muqattaʿāt断章字母（有则展示，无不展示）',
    `arabic_text` LONGTEXT COMMENT '阿拉伯文原文',
    `indonesian_text` LONGTEXT COMMENT '印尼语翻译',
    `latin_text` LONGTEXT COMMENT '拉丁音译文本',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_doa_id` (`doa_id`) COMMENT '祈祷文ID索引',
    INDEX `idx_content_order` (`content_order`) COMMENT '内容排序索引',
    FOREIGN KEY (`doa_id`) REFERENCES `ramadhan_doa` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='斋月祷文内容表';
