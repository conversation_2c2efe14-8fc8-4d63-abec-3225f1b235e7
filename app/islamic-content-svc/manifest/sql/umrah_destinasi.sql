-- 副朝地标类型表
DROP TABLE IF EXISTS `umrah_landmark_type`;
CREATE TABLE `umrah_landmark_type` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `icon_url` VARCHAR(255) COMMENT '图标路径',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝地标类型表';

-- 副朝地标类型多语言表
DROP TABLE IF EXISTS `umrah_landmark_type_languages`;
CREATE TABLE `umrah_landmark_type_languages` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type_id` BIGINT UNSIGNED NOT NULL COMMENT '地标类型ID，关联umrah_landmark_type.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '语言ID：0-中文，1-英文，2-印尼语',
    `type_name` VARCHAR(100) NOT NULL COMMENT '类型名称',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_language` (`type_id`, `language_id`) COMMENT '地标类型ID和语言唯一索引',
    INDEX `idx_type_id` (`type_id`) COMMENT '地标类型ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    FOREIGN KEY (`type_id`) REFERENCES `umrah_landmark_type` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝地标类型多语言表';

-- 副朝地标表
DROP TABLE IF EXISTS `umrah_landmark`;
CREATE TABLE `umrah_landmark` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type_id` BIGINT UNSIGNED NOT NULL COMMENT '地标类型ID，关联umrah_landmark_type.id',
    `inner_type` VARCHAR(100) NOT NULL DEFAULT 'destinasi' COMMENT '内部类型: (destinasi, tokoh)',
    `latitude` DECIMAL(11, 8) COMMENT '纬度',
    `longitude` DECIMAL(11, 8) COMMENT '经度',
    `image_url` VARCHAR(500) COMMENT '图片URL',
    `sort_order` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序值，数字越小排序越靠前',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_type_id` (`type_id`) COMMENT '地标类型ID索引',
    INDEX `idx_inner_type` (`inner_type`) COMMENT '内部类型索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    FOREIGN KEY (`type_id`) REFERENCES `umrah_landmark_type` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝地标表';

-- 副朝地标多语言表
DROP TABLE IF EXISTS `umrah_landmark_languages`;
CREATE TABLE `umrah_landmark_languages` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `landmark_id` BIGINT UNSIGNED NOT NULL COMMENT '地标ID，关联umrah_landmark.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '语言ID：0-中文，1-英文，2-印尼语',
    `landmark_name` VARCHAR(255) NOT NULL COMMENT '地标名称',
    `country` VARCHAR(100) NOT NULL COMMENT '国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)',
    `address` TEXT NOT NULL COMMENT '详细地址',
    `short_description` TEXT COMMENT '简介',
    `information_text` LONGTEXT COMMENT '详细介绍',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_landmark_language` (`landmark_id`, `language_id`) COMMENT '地标ID和语言唯一索引',
    INDEX `idx_landmark_id` (`landmark_id`) COMMENT '地标ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    FOREIGN KEY (`landmark_id`) REFERENCES `umrah_landmark` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝地标多语言表';
