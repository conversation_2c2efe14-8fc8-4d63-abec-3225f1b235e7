-- 副朝仪式顺序表 - 基础信息
DROP TABLE IF EXISTS `umrah_urutan`;
CREATE TABLE `umrah_urutan` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `urutan_no` INT NOT NULL COMMENT '副朝顺序（数字）',
    `icon_url` VARCHAR(500) COMMENT '图标URL',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_urutan_no` (`urutan_no`) COMMENT '副朝顺序唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝仪式顺序基础信息表';

-- 副朝仪式顺序表 - 多语言内容
DROP TABLE IF EXISTS `umrah_urutan_content`;
CREATE TABLE `umrah_urutan_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `urutan_id` BIGINT UNSIGNED NOT NULL COMMENT '副朝顺序ID，关联umrah_urutan.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `urutan_name` VARCHAR(60) NOT NULL COMMENT '副朝仪式名称（最多60个字符）',
    `urutan_time` VARCHAR(255) COMMENT '仪式时间',
    `urutan_content` LONGTEXT COMMENT '仪式内容描述（富文本）',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_urutan_language` (`urutan_id`, `language_id`) COMMENT '副朝顺序ID和语言唯一索引',
    INDEX `idx_urutan_id` (`urutan_id`) COMMENT '副朝顺序ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    INDEX `idx_urutan_name` (`urutan_name`) COMMENT '副朝仪式名称索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝仪式顺序多语言内容表';

