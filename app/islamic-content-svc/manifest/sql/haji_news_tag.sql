-- 朝觐新闻标签主表
DROP TABLE IF EXISTS `haji_news_tag`;
CREATE TABLE `haji_news_tag` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `article_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联文章数（不一定使用这个字段，可由关联表实时计算）',
    `sort_order` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序值，数字越小排序越靠前',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐新闻标签主表';

-- 朝觐新闻标签多语言表
DROP TABLE IF EXISTS `haji_news_tag_languages`;
CREATE TABLE `haji_news_tag_languages` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tag_id` BIGINT UNSIGNED NOT NULL COMMENT '标签ID，关联haji_news_tag.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '语言ID：0-中文，1-英文，2-印尼语',
    `tag_name` VARCHAR(200) NOT NULL COMMENT '标签名称',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tag_language` (`tag_id`, `language_id`) COMMENT '标签ID和语言唯一索引',
    INDEX `idx_tag_id` (`tag_id`) COMMENT '标签ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    FOREIGN KEY (`tag_id`) REFERENCES `haji_news_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐新闻标签多语言表';

-- 朝觐新闻标签与文章关联表
DROP TABLE IF EXISTS `haji_news_tag_article`;
CREATE TABLE `haji_news_tag_article` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tag_id` BIGINT UNSIGNED NOT NULL COMMENT '标签ID，关联haji_news_tag.id',
    `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID，关联news_article.id',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tag_article` (`tag_id`, `article_id`) COMMENT '同一文章在同一标签下唯一',
    INDEX `idx_tag_id` (`tag_id`) COMMENT '标签ID索引',
    INDEX `idx_article_id` (`article_id`) COMMENT '文章ID索引',
    FOREIGN KEY (`tag_id`) REFERENCES `haji_news_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `news_article` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐新闻标签与文章关联表';

