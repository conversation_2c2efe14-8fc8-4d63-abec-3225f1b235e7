-- 副朝智慧
DROP TABLE IF EXISTS `umrah_hikmah`;
CREATE TABLE `umrah_hikmah` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `article_id` BIGINT UNSIGNED NOT NULL COMMENT '文章ID，关联news_article.id',
    `sort_order` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序值，数字越小排序越靠前',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_article_id` (`article_id`) COMMENT '文章ID索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝智慧表';

-- 副朝智慧多语言表
DROP TABLE IF EXISTS `umrah_hikmah_languages`;
CREATE TABLE `umrah_hikmah_languages` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `hikmah_id` BIGINT UNSIGNED NOT NULL COMMENT '副朝智慧ID，关联umrah_hikmah.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '语言ID：0-中文，1-英文，2-印尼语',
    `title` VARCHAR(100) NOT NULL COMMENT '标题',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_hikmah_language` (`hikmah_id`, `language_id`) COMMENT '副朝智慧ID和语言唯一索引',
    INDEX `idx_hikmah_id` (`hikmah_id`) COMMENT '副朝智慧ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    INDEX `idx_title` (`title`) COMMENT '标题索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='副朝智慧多语言表';
