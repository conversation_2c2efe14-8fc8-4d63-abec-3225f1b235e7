// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message AdvertisementBanner {
    uint32 Id              = 1; // 主键ID
    uint32 AdvertisementId = 2; // 广告ID
    uint32 LanguageId      = 3; // 语言ID: 0-中文, 1-英文, 2-印尼语
    string ImageUrl        = 4; // Banner图片URL
    string LinkUrl         = 5; // 跳转链接URL
    uint32 SortOrder       = 6; // 图片排序权重，数字越小越靠前
    uint64 CreateTime      = 7; // 创建时间(毫秒时间戳)
    uint64 UpdateTime      = 8; // 更新时间(毫秒时间戳)
}