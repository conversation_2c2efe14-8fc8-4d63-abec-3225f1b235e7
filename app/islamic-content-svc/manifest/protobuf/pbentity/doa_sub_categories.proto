// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message DoaSubCategories {
    int32  Id          = 1; //
    int32  ParentId    = 2; //
    string Name        = 3; //
    string Slug        = 4; //
    string Description = 5; //
    int32  OrderNumber = 6; //
    int32  ViewCount   = 7; //
    string ParentName  = 8; //
    int32  Total       = 9; //
}