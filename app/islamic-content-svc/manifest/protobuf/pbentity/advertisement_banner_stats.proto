// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message AdvertisementBannerStats {
    uint64 Id              = 1;  // 主键ID
    uint32 BannerId        = 2;  // 广告ID
    uint32 AdvertisementId = 3;  // 广告ID
    uint32 LanguageId      = 4;  // 语言ID: 0-中文, 1-英文, 2-印尼语
    uint64 UserId          = 5;  // 用户ID，0表示未登录用户
    string ActionType      = 6;  // 操作类型(click/view)
    string DeviceId        = 7;  // 设备唯一标识
    string IpAddress       = 8;  // IP地址
    string UserAgent       = 9;  // 用户代理信息
    uint64 CreateTime      = 10; // 操作时间(毫秒时间戳)
}