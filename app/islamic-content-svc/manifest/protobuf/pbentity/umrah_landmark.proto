// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message UmrahLandmark {
    uint64 Id         = 1; // 主键ID
    uint64 TypeId     = 2; // 地标类型ID，关联umrah_landmark_type.id
    string InnerType  = 3; // 内部类型: (destinasi, tokoh)
    string Latitude   = 4; // 纬度
    string Longitude  = 5; // 经度
    string ImageUrl   = 6; // 图片URL
    uint32 SortOrder  = 7; // 排序值，数字越小排序越靠前
    uint64 CreateTime = 8; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 9; // 更新时间（毫秒时间戳）
}