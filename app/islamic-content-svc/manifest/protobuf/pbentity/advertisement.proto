// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message Advertisement {
    uint32 Id           = 1; // 主键ID
    string PositionCode = 2; // 广告位置编码
    uint32 SortOrder    = 3; // 排序权重，数字越小越靠前
    uint32 Status       = 4; // 状态: 0-禁用, 1-启用
    uint64 StartTime    = 5; // 开始时间戳(毫秒)
    uint64 EndTime      = 6; // 结束时间戳(毫秒)
    uint32 AdminId      = 7; // 创建管理员ID
    uint64 CreateTime   = 8; // 创建时间(毫秒时间戳)
    uint64 UpdateTime   = 9; // 更新时间(毫秒时间戳)
}