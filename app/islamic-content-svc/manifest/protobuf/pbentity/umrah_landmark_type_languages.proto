// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message UmrahLandmarkTypeLanguages {
    uint64 Id         = 1; // 主键ID
    uint64 TypeId     = 2; // 地标类型ID，关联umrah_landmark_type.id
    uint32 LanguageId = 3; // 语言ID：0-中文，1-英文，2-印尼语
    string TypeName   = 4; // 类型名称
    uint64 CreateTime = 5; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 6; // 更新时间（毫秒时间戳）
}