// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message AdvertisementPosition {
    uint32 Id           = 1; // 主键ID
    string PositionName = 2; // 广告位名称，不可修改
    string PositionCode = 3; // 位置编码，自动生成，不可修改
    string Remark       = 4; // 备注，对该广告位置的备注，可修改
    uint64 CreateTime   = 5; // 创建时间(毫秒时间戳)
    uint64 UpdateTime   = 6; // 更新时间(毫秒时间戳)
}