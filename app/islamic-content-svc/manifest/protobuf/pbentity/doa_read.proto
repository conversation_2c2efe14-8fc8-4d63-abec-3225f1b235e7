// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message DoaRead {
    uint32 Id         = 1; //
    uint32 UserId     = 2; // 用户id
    uint32 Types      = 3; // 类型 1-doa,2-wirid- 废弃
    uint32 PId        = 4; // 父级id
    uint32 BaccanId   = 5; // baccan_id-sub-cate-id
    string PName      = 6; // 父级名称
    string BaccanName = 7; // 名称
    int64  CreateTime = 8; // 创建时间（注册时间）
    int64  UpdateTime = 9; // 更新时间，0代表创建后未更新
}