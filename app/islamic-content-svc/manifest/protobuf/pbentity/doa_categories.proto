// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message DoaCategories {
    int32  Id          = 1; //
    string Name        = 2; //
    string Slug        = 3; //
    int32  OrderNumber = 4; //
    int32  ViewCount   = 5; //
    string GroupName   = 6; //
    int32  Total       = 7; //
}