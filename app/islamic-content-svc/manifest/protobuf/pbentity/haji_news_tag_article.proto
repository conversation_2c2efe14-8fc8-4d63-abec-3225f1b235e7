// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiNewsTagArticle {
    uint64 Id         = 1; // 主键ID
    uint64 TagId      = 2; // 标签ID，关联haji_news_tag.id
    uint32 ArticleId  = 3; // 文章ID，关联news_article.id
    uint64 CreateTime = 4; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 5; // 更新时间（毫秒时间戳）
}