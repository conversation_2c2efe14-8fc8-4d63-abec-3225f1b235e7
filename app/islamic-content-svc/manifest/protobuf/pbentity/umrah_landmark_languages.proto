// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message UmrahLandmarkLanguages {
    uint64 Id               = 1;  // 主键ID
    uint64 LandmarkId       = 2;  // 地标ID，关联umrah_landmark.id
    uint32 LanguageId       = 3;  // 语言ID：0-中文，1-英文，2-印尼语
    string LandmarkName     = 4;  // 地标名称
    string Country          = 5;  // 国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)
    string Address          = 6;  // 详细地址
    string ShortDescription = 7;  // 简介
    string InformationText  = 8;  // 详细介绍
    uint64 CreateTime       = 9;  // 创建时间（毫秒时间戳）
    uint64 UpdateTime       = 10; // 更新时间（毫秒时间戳）
}