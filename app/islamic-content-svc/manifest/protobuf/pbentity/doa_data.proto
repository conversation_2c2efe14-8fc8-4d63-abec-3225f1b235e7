// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message DoaData {
    int32  Id              = 1; //
    int32  CategoryId      = 2; //
    string Arabic          = 3; //
    string Translate       = 4; //
    string Transliteration = 5; //
    int32  Type            = 6; //
    int32  OrderNumber     = 7; //
}