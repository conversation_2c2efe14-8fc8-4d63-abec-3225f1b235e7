// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiNewsTag {
    uint64 Id           = 1; // 主键ID
    uint32 ArticleCount = 2; // 关联文章数（不一定使用这个字段，可由关联表实时计算）
    uint32 SortOrder    = 3; // 排序值，数字越小排序越靠前
    uint64 CreateTime   = 4; // 创建时间（毫秒时间戳）
    uint64 UpdateTime   = 5; // 更新时间（毫秒时间戳）
}