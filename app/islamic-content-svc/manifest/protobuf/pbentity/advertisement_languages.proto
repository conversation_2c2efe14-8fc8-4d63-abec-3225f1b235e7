// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message AdvertisementLanguages {
    uint32 Id               = 1;  // 主键ID
    uint32 AdvertisementId  = 2;  // 广告ID
    uint32 LanguageId       = 3;  // 语言ID: 0-中文, 1-英文, 2-印尼语
    string Title            = 4;  // 广告名称
    string Description      = 5;  // 广告描述
    uint32 DisplayType      = 6;  // 显示类型: 1-单图固定, 2-多图轮播
    uint32 CarouselInterval = 7;  // 轮播间隔时间(秒)，仅多图轮播时有效
    uint32 AdminId          = 8;  // 创建管理员ID
    uint64 CreateTime       = 9;  // 创建时间(毫秒时间戳)
    uint64 UpdateTime       = 10; // 更新时间(毫秒时间戳)
}