package islamic

import (
	"context"
	"github.com/golang/protobuf/proto"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
)

type ControllerCheckUpdate struct {
	v1.UnimplementedCheckUpdateServiceServer
}

func (*ControllerCheckUpdate) CheckUpdate(ctx context.Context, req *v1.CheckUpdateReq) (res *v1.CheckUpdateRes, err error) {

	return &v1.CheckUpdateRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.CheckUpdateData{
			IsUpdate:      proto.Bool(false),
			ForceUpdate:   proto.Bool(false),
			LatestVersion: proto.String("1.0.1"),
			UpdateUrl:     proto.String("https://github.com/halalplus/islamic-content-svc"),
			FileSize:      proto.Int32(0),
			Md5:           proto.String(""),
		},
	}, nil
}
