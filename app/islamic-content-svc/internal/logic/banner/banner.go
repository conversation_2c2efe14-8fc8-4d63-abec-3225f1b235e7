package banner

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"

	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/do"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/token"
)

type sBanner struct{}

func init() {
	service.RegisterBanner(New())
}

func New() service.IBanner {
	return &sBanner{}
}

// BannerList 获取Banner列表
func (s *sBanner) BannerList(ctx context.Context, languageId uint, bannerType string) ([]*model.BannerInfo, error) {
	// 使用新的advertisement表结构
	// 查询上线状态的广告（同一个position，status为1的只会有一条）
	adQuery := dao.Advertisement.Ctx(ctx).Where(dao.Advertisement.Columns().Status, 1)

	// 按位置编码筛选（bannerType对应positionCode）
	if len(bannerType) > 0 {
		adQuery = adQuery.Where(dao.Advertisement.Columns().PositionCode, bannerType)
	} else {
		adQuery = adQuery.Where(dao.Advertisement.Columns().PositionCode, "home")
	}

	var advertisement entity.Advertisement
	err := adQuery.Scan(&advertisement)
	if err != nil {
		g.Log().Error(ctx, "查询广告失败:", err)
		return nil, err
	}

	// 如果没有找到广告，返回空列表
	if advertisement.Id == 0 {
		return []*model.BannerInfo{}, nil
	}

	// 查询多语言信息
	langQuery := dao.AdvertisementLanguages.Ctx(ctx).
		Where(dao.AdvertisementLanguages.Columns().AdvertisementId, advertisement.Id).
		Where(dao.AdvertisementLanguages.Columns().LanguageId, languageId)

	var adLanguages []*entity.AdvertisementLanguages
	err = langQuery.Scan(&adLanguages)
	if err != nil {
		g.Log().Error(ctx, "查询广告多语言信息失败:", err)
		return nil, err
	}

	// 查询banner图片信息
	bannerQuery := dao.AdvertisementBanner.Ctx(ctx).
		Where(dao.AdvertisementBanner.Columns().AdvertisementId, advertisement.Id).
		Where(dao.AdvertisementBanner.Columns().LanguageId, languageId).
		Order(dao.AdvertisementBanner.Columns().SortOrder)

	var adBanners []*entity.AdvertisementBanner
	err = bannerQuery.Scan(&adBanners)
	if err != nil {
		g.Log().Error(ctx, "查询广告Banner信息失败:", err)
		return nil, err
	}

	// 映射
	langMap := make(map[uint]*entity.AdvertisementLanguages)
	for _, lang := range adLanguages {
		langMap[lang.AdvertisementId] = lang
	}

	bannerMap := make(map[uint][]*entity.AdvertisementBanner)
	for _, banner := range adBanners {
		bannerMap[banner.AdvertisementId] = append(bannerMap[banner.AdvertisementId], banner)
	}

	// 转换
	var bannerInfos []*model.BannerInfo
	lang, hasLang := langMap[advertisement.Id]
	banners, hasBanners := bannerMap[advertisement.Id]

	if hasLang && hasBanners && len(banners) > 0 {
		// 为每个banner图片创建一个BannerInfo
		for _, banner := range banners {
			bannerInfo := &model.BannerInfo{
				Id:          uint32(banner.Id), // 使用banner的ID作为BannerInfo的ID
				LanguageId:  uint32(banner.LanguageId),
				Title:       lang.Title,
				Description: lang.Description,
				ImageUrl:    banner.ImageUrl,
				LinkUrl:     banner.LinkUrl,
				SortOrder:   uint32(banner.SortOrder),
			}
			bannerInfos = append(bannerInfos, bannerInfo)
		}
	}

	return bannerInfos, nil
}

// BannerClick 记录Banner点击统计
// 这个是记录点击统计的，不返回任何结果
func (s *sBanner) BannerClick(ctx context.Context, in *model.BannerClickInput) {
	// 验证Banner是否存在（使用新的advertisement_banner表）
	bannerExists, err := dao.AdvertisementBanner.Ctx(ctx).
		Where(dao.AdvertisementBanner.Columns().Id, in.BannerId).Count()
	if err != nil || bannerExists == 0 {
		g.Log().Error(ctx, "查询Banner失败:", err)
		return
	}

	// 不处理这个可能的失败。因为也支持非登陆的情况下点击
	uid, _ := token.GetUserIdByToken(ctx)

	// 获取客户端IP和User-Agent
	var clientIP, userAgent string
	if req := g.RequestFromCtx(ctx); req != nil {
		clientIP = req.GetClientIp()
		userAgent = req.Header.Get("User-Agent")
	}

	// 如果从请求中获取不到，使用传入的device_id作为标识
	if clientIP == "" {
		clientIP = "unknown"
	}
	if userAgent == "" {
		userAgent = "unknown"
	}

	// 记录点击统计（使用新的advertisement_banner_stats表）
	statsData := &do.AdvertisementBannerStats{
		BannerId:   in.BannerId,
		UserId:     uid,
		ActionType: "click", // 点击类型
		DeviceId:   in.DeviceId,
		IpAddress:  clientIP,
		UserAgent:  userAgent,
	}

	_, err = dao.AdvertisementBannerStats.Ctx(ctx).Data(statsData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录Banner点击统计失败:", err)
		return
	}

	// log做统计用？
	g.Log().Info(ctx, "Banner点击统计记录成功", g.Map{
		"banner_id": in.BannerId,
		"user_id":   0,
		"device_id": in.DeviceId,
		"ip":        clientIP,
	})
}

// BannerView 记录Banner浏览统计
// 这个是记录浏览统计的，支持批量上报多个banner的浏览数据
func (s *sBanner) BannerView(ctx context.Context, in *model.BannerViewInput) {
	// 验证输入参数
	if len(in.BannerIds) == 0 {
		g.Log().Warning(ctx, "Banner浏览上报参数为空")
		return
	}

	// 不处理这个可能的失败。因为也支持非登陆的情况下浏览
	uid, _ := token.GetUserIdByToken(ctx)

	// 获取客户端IP和User-Agent
	var clientIP, userAgent string
	if req := g.RequestFromCtx(ctx); req != nil {
		clientIP = req.GetClientIp()
		userAgent = req.Header.Get("User-Agent")
	}

	// 如果从请求中获取不到，使用传入的device_id作为标识
	if clientIP == "" {
		clientIP = "unknown"
	}
	if userAgent == "" {
		userAgent = "unknown"
	}

	// 批量记录浏览统计
	for _, bannerId := range in.BannerIds {
		// 验证Banner是否存在（使用新的advertisement_banner表）
		bannerExists, err := dao.AdvertisementBanner.Ctx(ctx).
			Where(dao.AdvertisementBanner.Columns().Id, bannerId).Count()
		if err != nil || bannerExists == 0 {
			g.Log().Warning(ctx, "Banner不存在，跳过浏览统计", g.Map{
				"banner_id": bannerId,
				"error":     err,
			})
			continue
		}

		// 记录浏览统计（使用新的advertisement_banner_stats表）
		statsData := &do.AdvertisementBannerStats{
			BannerId:   bannerId,
			UserId:     uid,
			ActionType: "view", // 浏览类型
			DeviceId:   in.DeviceId,
			IpAddress:  clientIP,
			UserAgent:  userAgent,
		}

		_, err = dao.AdvertisementBannerStats.Ctx(ctx).Data(statsData).Insert()
		if err != nil {
			g.Log().Error(ctx, "记录Banner浏览统计失败", g.Map{
				"banner_id": bannerId,
				"error":     err,
			})
			continue
		}

		// log做统计用
		g.Log().Info(ctx, "Banner浏览统计记录成功", g.Map{
			"banner_id": bannerId,
			"user_id":   uid,
			"device_id": in.DeviceId,
			"ip":        clientIP,
		})
	}
}
