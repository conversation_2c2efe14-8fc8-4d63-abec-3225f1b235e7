package prayer

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetUmrahUrutanList 获取副朝仪式顺序列表
func (s *sPrayer) GetUmrahUrutanList(ctx context.Context, languageId uint) (*model.UmrahUrutanListOutput, error) {
	var urutanList []*model.UmrahUrutanInfo
	err := dao.UmrahUrutan.Ctx(ctx).As("uu").
		Fields("uu.id, uu.urutan_no, uuc.urutan_name, uuc.urutan_time, uu.icon_url, '' as urutan_content").
		LeftJoin(dao.UmrahUrutanContent.Table()+" uuc", "uuc.urutan_id = uu.id").
		Where("uuc.language_id", languageId).
		Order("uu.urutan_no ASC").
		Scan(&urutanList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝仪式顺序列表失败:", err)
		return nil, err
	}

	return &model.UmrahUrutanListOutput{
		List: urutanList,
	}, nil
}

// GetUmrahUrutanDetail 获取副朝仪式顺序详情
func (s *sPrayer) GetUmrahUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.UmrahUrutanDetailOutput, error) {
	var urutan model.UmrahUrutanInfo
	err := dao.UmrahUrutan.Ctx(ctx).As("uu").
		Fields("uu.id, uu.urutan_no, uuc.urutan_name, uuc.urutan_time, uu.icon_url, uuc.urutan_content").
		LeftJoin(dao.UmrahUrutanContent.Table()+" uuc", "uuc.urutan_id = uu.id").
		Where("uu.id", urutanId).
		Where("uuc.language_id", languageId).
		Scan(&urutan)
	if err != nil {
		g.Log().Error(ctx, "查询副朝仪式顺序详情失败:", err)
		return nil, err
	}

	if urutan.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.UmrahUrutanDetailOutput{
		Urutan: &urutan,
	}, nil
}

// GetUmrahDoaRingkasList 获取副朝祈祷文简要列表
func (s *sPrayer) GetUmrahDoaRingkasList(ctx context.Context) ([]*model.UmrahDoaRingkasInfo, error) {
	result, err := s.queryDoaData(ctx, "doa", "doa-haji-umrah", 0)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文数据失败:", err)
		return nil, err
	}

	if result.Category == nil {
		g.Log().Warning(ctx, "未找到副朝祈祷文分类")
		return []*model.UmrahDoaRingkasInfo{}, nil
	}

	if len(result.SubCategories) == 0 {
		return []*model.UmrahDoaRingkasInfo{}, nil
	}

	var (
		doaList   = []*model.UmrahDoaRingkasInfo{}
		doaMap    = make(map[int]*model.UmrahDoaRingkasInfo)
		bacaanIds = []uint64{}
	)

	for _, subCat := range result.SubCategories {
		doaInfo := &model.UmrahDoaRingkasInfo{
			Id:          uint64(subCat.Id),
			DoaNo:       subCat.OrderNumber,
			DoaName:     subCat.Name,
			BacaanId:    uint64(subCat.Id),
			IsCollected: false,
			Contents:    []*model.UmrahDoaRingkasContentInfo{},
		}
		doaList = append(doaList, doaInfo)
		doaMap[subCat.Id] = doaInfo
		bacaanIds = append(bacaanIds, uint64(subCat.Id))
	}

	// 批量查询收藏状态
	collectStatusMap := s.batchCheckDoaCollectStatus(ctx, bacaanIds)

	// 组装内容
	for _, doaData := range result.DoaDataList {
		if doa, exists := doaMap[doaData.CategoryId]; exists {
			title, arabicText, indonesianText, latinText := buildDoaContent(doaData)
			contentInfo := &model.UmrahDoaRingkasContentInfo{
				Id:             uint64(doaData.Id),
				ContentOrder:   doaData.OrderNumber,
				Title:          title,
				MuqattaAt:      "",
				ArabicText:     arabicText,
				IndonesianText: indonesianText,
				LatinText:      latinText,
				ContentType:    doaData.Type,
			}
			doa.IsCollected = collectStatusMap[uint64(doa.BacaanId)]
			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}

// GetUmrahDoaPanjangList 获取副朝祈祷文详细列表
func (s *sPrayer) GetUmrahDoaPanjangList(ctx context.Context) ([]*model.UmrahDoaPanjangInfo, error) {
	var categories []*entity.DoaCategories
	err := dao.DoaCategories.Ctx(ctx).
		Fields("id, name, order_number, total").
		Where("group_name", "haji-dan-umrah").
		Order("id ASC").
		Scan(&categories)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文详细列表失败:", err)
		return nil, err
	}

	var doaList []*model.UmrahDoaPanjangInfo
	for i, category := range categories {
		doaInfo := &model.UmrahDoaPanjangInfo{
			Id:          uint64(category.Id),
			DoaNo:       i + 1,
			DoaName:     category.Name,
			BacaanCount: category.Total,
		}
		doaList = append(doaList, doaInfo)
	}

	return doaList, nil
}

// GetUmrahDoaPanjangBacaanList 获取副朝祈祷文诵读内容列表
func (s *sPrayer) GetUmrahDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.UmrahDoaPanjangBacaanListOutput, error) {
	result, err := s.queryDoaData(ctx, "", "", doaId)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文数据失败:", err)
		return nil, err
	}

	if result.Category == nil {
		return nil, gerror.New("not found")
	}

	if len(result.SubCategories) == 0 {
		return &model.UmrahDoaPanjangBacaanListOutput{
			List:    []*model.UmrahDoaPanjangBacaanInfo{},
			DoaNo:   result.Category.OrderNumber,
			DoaName: result.Category.Name,
		}, nil
	}

	// 构建诵读列表
	var (
		bacaanList = []*model.UmrahDoaPanjangBacaanInfo{}
		bacaanMap  = make(map[int]*model.UmrahDoaPanjangBacaanInfo)
		bacaanIds  = []uint64{}
	)

	for _, subCat := range result.SubCategories {
		bacaanInfo := &model.UmrahDoaPanjangBacaanInfo{
			Id:          uint64(subCat.Id),
			DoaId:       doaId,
			BacaanNo:    subCat.OrderNumber,
			BacaanName:  subCat.Name,
			BacaanId:    uint64(subCat.Id),
			IsCollected: false,
			Contents:    []*model.UmrahDoaPanjangBacaanContentInfo{},
		}
		bacaanList = append(bacaanList, bacaanInfo)
		bacaanMap[subCat.Id] = bacaanInfo
		bacaanIds = append(bacaanIds, uint64(subCat.Id))
	}

	// 批量查询收藏状态
	collectStatusMap := s.batchCheckDoaCollectStatus(ctx, bacaanIds)

	// 组装祈祷文内容
	for _, doaData := range result.DoaDataList {
		if bacaan, exists := bacaanMap[doaData.CategoryId]; exists {
			title, arabicText, indonesianText, latinText := buildDoaContent(doaData)
			contentInfo := &model.UmrahDoaPanjangBacaanContentInfo{
				Id:             uint64(doaData.Id),
				ContentOrder:   doaData.OrderNumber,
				Title:          title,
				MuqattaAt:      "",
				ArabicText:     arabicText,
				IndonesianText: indonesianText,
				LatinText:      latinText,
				ContentType:    doaData.Type,
			}
			bacaan.IsCollected = collectStatusMap[uint64(bacaan.BacaanId)]
			bacaan.Contents = append(bacaan.Contents, contentInfo)
		}
	}

	return &model.UmrahDoaPanjangBacaanListOutput{
		List:    bacaanList,
		DoaNo:   result.Category.OrderNumber,
		DoaName: result.Category.Name,
	}, nil
}

// GetUmrahHikmahList 获取副朝智慧列表
func (s *sPrayer) GetUmrahHikmahList(ctx context.Context, languageId uint) (*model.UmrahHikmahListOutput, error) {
	var hikmahList []*model.UmrahHikmahInfo
	err := dao.UmrahHikmah.Ctx(ctx).As("uh").
		Fields("uh.id, uh.article_id, uhl.title").
		LeftJoin(dao.UmrahHikmahLanguages.Table()+" uhl", "uhl.hikmah_id = uh.id").
		Where("uhl.language_id", languageId).
		Order("uh.sort_order ASC, uh.id ASC").
		Scan(&hikmahList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝智慧列表失败:", err)
		return nil, err
	}

	return &model.UmrahHikmahListOutput{
		List: hikmahList,
	}, nil
}

// GetUmrahLandmarkList 获取副朝地标列表（支持分页）
func (s *sPrayer) GetUmrahLandmarkList(ctx context.Context, innerType string, languageId uint, page, size int) (*model.UmrahLandmarkListOutput, error) {
	baseQuery := dao.UmrahLandmark.Ctx(ctx).As("ul").
		LeftJoin(dao.UmrahLandmarkLanguages.Table()+" ull", "ull.landmark_id = ul.id").
		LeftJoin(dao.UmrahLandmarkType.Table()+" ult", "ult.id = ul.type_id").
		LeftJoin(dao.UmrahLandmarkTypeLanguages.Table()+" ultl", "ultl.type_id = ul.type_id").
		Where("ull.language_id", languageId).
		Where("ultl.language_id", languageId)

	// 内部类型, destinasi(目的地), tokoh(人物)
	if innerType != "" {
		baseQuery = baseQuery.Where("ul.inner_type", innerType)
	}

	// 查询总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标总数失败:", err)
		return nil, err
	}

	// 查询分页数据
	dataQuery := baseQuery.
		Fields("ul.id, ul.latitude, ul.longitude, ul.image_url, ull.landmark_name, ult.icon_url, ultl.type_name").
		Order("ul.sort_order ASC, ul.id ASC").
		Page(page, size)

	type landmarkQueryResult struct {
		Id           uint64  `json:"id"`
		Latitude     float64 `json:"latitude"`
		Longitude    float64 `json:"longitude"`
		ImageUrl     string  `json:"image_url"`
		LandmarkName string  `json:"landmark_name"`
		IconUrl      string  `json:"icon_url"`
		TypeName     string  `json:"type_name"`
	}

	var results []*landmarkQueryResult
	err = dataQuery.Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标列表失败:", err)
		return nil, err
	}

	landmarkList := make([]*model.UmrahLandmarkItem, 0, len(results))
	for _, result := range results {
		landmarkList = append(landmarkList, &model.UmrahLandmarkItem{
			LandmarkId:   result.Id,
			Latitude:     result.Latitude,
			Longitude:    result.Longitude,
			ImageUrl:     result.ImageUrl,
			LandmarkName: result.LandmarkName,
			TypeIconUrl:  result.IconUrl,
			TypeName:     result.TypeName,
		})
	}

	return &model.UmrahLandmarkListOutput{
		List:  landmarkList,
		Total: total,
	}, nil
}

// GetUmrahLandmarkDetail 获取副朝地标详情
func (s *sPrayer) GetUmrahLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.UmrahLandmarkInfo, error) {
	type landmarkDetailResult struct {
		Id               uint64  `json:"id"`
		Latitude         float64 `json:"latitude"`
		Longitude        float64 `json:"longitude"`
		ImageUrl         string  `json:"image_url"`
		LandmarkName     string  `json:"landmark_name"`
		Country          string  `json:"country"`
		Address          string  `json:"address"`
		ShortDescription string  `json:"short_description"`
		InformationText  string  `json:"information_text"`
		IconUrl          string  `json:"icon_url"`
		TypeName         string  `json:"type_name"`
	}

	var result landmarkDetailResult
	err := dao.UmrahLandmark.Ctx(ctx).As("ul").
		LeftJoin(dao.UmrahLandmarkLanguages.Table()+" ull", "ull.landmark_id = ul.id").
		LeftJoin(dao.UmrahLandmarkType.Table()+" ult", "ult.id = ul.type_id").
		LeftJoin(dao.UmrahLandmarkTypeLanguages.Table()+" ultl", "ultl.type_id = ul.type_id").
		Where("ul.id", landmarkId).
		Where("ull.language_id", languageId).
		Where("ultl.language_id", languageId).
		Fields("ul.id, ul.latitude, ul.longitude, ul.image_url, ull.landmark_name, ull.country, ull.address, ull.short_description, ull.information_text, ult.icon_url, ultl.type_name").
		Scan(&result)
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标详情失败:", err)
		return nil, err
	}

	if result.Id == 0 {
		return nil, gerror.New("data not found")
	}

	return &model.UmrahLandmarkInfo{
		LandmarkId:       result.Id,
		Latitude:         result.Latitude,
		Longitude:        result.Longitude,
		ImageUrl:         result.ImageUrl,
		LandmarkName:     result.LandmarkName,
		Country:          result.Country,
		Address:          result.Address,
		ShortDescription: result.ShortDescription,
		InformationText:  result.InformationText,
		TypeIconUrl:      result.IconUrl,
		TypeName:         result.TypeName,
	}, nil
}
