// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// umrahUrutanContentDao is the data access object for the table umrah_urutan_content.
// You can define custom methods on it to extend its functionality as needed.
type umrahUrutanContentDao struct {
	*internal.UmrahUrutanContentDao
}

var (
	// UmrahUrutanContent is a globally accessible object for table umrah_urutan_content operations.
	UmrahUrutanContent = umrahUrutanContentDao{internal.NewUmrahUrutanContentDao()}
)

// Add your custom methods and functionality below.
