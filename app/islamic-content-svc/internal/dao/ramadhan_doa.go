// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// ramadhanDoaDao is the data access object for the table ramadhan_doa.
// You can define custom methods on it to extend its functionality as needed.
type ramadhanDoaDao struct {
	*internal.RamadhanDoaDao
}

var (
	// RamadhanDoa is a globally accessible object for table ramadhan_doa operations.
	RamadhanDoa = ramadhanDoaDao{internal.NewRamadhanDoaDao()}
)

// Add your custom methods and functionality below.
