// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// umrahDoaPanjangDao is the data access object for the table umrah_doa_panjang.
// You can define custom methods on it to extend its functionality as needed.
type umrahDoaPanjangDao struct {
	*internal.UmrahDoaPanjangDao
}

var (
	// UmrahDoaPanjang is a globally accessible object for table umrah_doa_panjang operations.
	UmrahDoaPanjang = umrahDoaPanjangDao{internal.NewUmrahDoaPanjangDao()}
)

// Add your custom methods and functionality below.
