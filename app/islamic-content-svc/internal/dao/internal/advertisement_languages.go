// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementLanguagesDao is the data access object for the table advertisement_languages.
type AdvertisementLanguagesDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  AdvertisementLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// AdvertisementLanguagesColumns defines and stores column names for the table advertisement_languages.
type AdvertisementLanguagesColumns struct {
	Id               string // 主键ID
	AdvertisementId  string // 广告ID
	LanguageId       string // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title            string // 广告名称
	Description      string // 广告描述
	DisplayType      string // 显示类型: 1-单图固定, 2-多图轮播
	CarouselInterval string // 轮播间隔时间(秒)，仅多图轮播时有效
	AdminId          string // 创建管理员ID
	CreateTime       string // 创建时间(毫秒时间戳)
	UpdateTime       string // 更新时间(毫秒时间戳)
}

// advertisementLanguagesColumns holds the columns for the table advertisement_languages.
var advertisementLanguagesColumns = AdvertisementLanguagesColumns{
	Id:               "id",
	AdvertisementId:  "advertisement_id",
	LanguageId:       "language_id",
	Title:            "title",
	Description:      "description",
	DisplayType:      "display_type",
	CarouselInterval: "carousel_interval",
	AdminId:          "admin_id",
	CreateTime:       "create_time",
	UpdateTime:       "update_time",
}

// NewAdvertisementLanguagesDao creates and returns a new DAO object for table data access.
func NewAdvertisementLanguagesDao(handlers ...gdb.ModelHandler) *AdvertisementLanguagesDao {
	return &AdvertisementLanguagesDao{
		group:    "default",
		table:    "advertisement_languages",
		columns:  advertisementLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdvertisementLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdvertisementLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdvertisementLanguagesDao) Columns() AdvertisementLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdvertisementLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdvertisementLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdvertisementLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
