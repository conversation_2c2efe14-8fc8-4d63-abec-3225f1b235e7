// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DoaDataDao is the data access object for the table doa_data.
type DoaDataDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  DoaDataColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// DoaDataColumns defines and stores column names for the table doa_data.
type DoaDataColumns struct {
	Id              string //
	CategoryId      string //
	Arabic          string //
	Translate       string //
	Transliteration string //
	Type            string //
	OrderNumber     string //
}

// doaDataColumns holds the columns for the table doa_data.
var doaDataColumns = DoaDataColumns{
	Id:              "id",
	CategoryId:      "category_id",
	Arabic:          "arabic",
	Translate:       "translate",
	Transliteration: "transliteration",
	Type:            "type",
	OrderNumber:     "order_number",
}

// NewDoaDataDao creates and returns a new DAO object for table data access.
func NewDoaDataDao(handlers ...gdb.ModelHandler) *DoaDataDao {
	return &DoaDataDao{
		group:    "default",
		table:    "doa_data",
		columns:  doaDataColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DoaDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DoaDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DoaDataDao) Columns() DoaDataColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DoaDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DoaDataDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DoaDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
