// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahLandmarkTypeDao is the data access object for the table umrah_landmark_type.
type UmrahLandmarkTypeDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  UmrahLandmarkTypeColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// UmrahLandmarkTypeColumns defines and stores column names for the table umrah_landmark_type.
type UmrahLandmarkTypeColumns struct {
	Id         string // 主键ID
	IconUrl    string // 图标路径
	IconType   string // 图标类型
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// umrahLandmarkTypeColumns holds the columns for the table umrah_landmark_type.
var umrahLandmarkTypeColumns = UmrahLandmarkTypeColumns{
	Id:         "id",
	IconUrl:    "icon_url",
	IconType:   "icon_type",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUmrahLandmarkTypeDao creates and returns a new DAO object for table data access.
func NewUmrahLandmarkTypeDao(handlers ...gdb.ModelHandler) *UmrahLandmarkTypeDao {
	return &UmrahLandmarkTypeDao{
		group:    "default",
		table:    "umrah_landmark_type",
		columns:  umrahLandmarkTypeColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahLandmarkTypeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahLandmarkTypeDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahLandmarkTypeDao) Columns() UmrahLandmarkTypeColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahLandmarkTypeDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahLandmarkTypeDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahLandmarkTypeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
