// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaPanjangDao is the data access object for the table umrah_doa_panjang.
type UmrahDoaPanjangDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  UmrahDoaPanjangColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// UmrahDoaPanjangColumns defines and stores column names for the table umrah_doa_panjang.
type UmrahDoaPanjangColumns struct {
	Id          string // 主键ID
	DoaNo       string // 祈祷文序号
	DoaName     string // 祈祷文名称
	BacaanCount string // 诵读数
	CreateTime  string // 创建时间（毫秒时间戳）
	UpdateTime  string // 更新时间（毫秒时间戳）
}

// umrahDoaPanjangColumns holds the columns for the table umrah_doa_panjang.
var umrahDoaPanjangColumns = UmrahDoaPanjangColumns{
	Id:          "id",
	DoaNo:       "doa_no",
	DoaName:     "doa_name",
	BacaanCount: "bacaan_count",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewUmrahDoaPanjangDao creates and returns a new DAO object for table data access.
func NewUmrahDoaPanjangDao(handlers ...gdb.ModelHandler) *UmrahDoaPanjangDao {
	return &UmrahDoaPanjangDao{
		group:    "default",
		table:    "umrah_doa_panjang",
		columns:  umrahDoaPanjangColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahDoaPanjangDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahDoaPanjangDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahDoaPanjangDao) Columns() UmrahDoaPanjangColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahDoaPanjangDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahDoaPanjangDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahDoaPanjangDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
