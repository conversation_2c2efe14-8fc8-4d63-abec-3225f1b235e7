// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DoaSubCategoriesDao is the data access object for the table doa_sub_categories.
type DoaSubCategoriesDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  DoaSubCategoriesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// DoaSubCategoriesColumns defines and stores column names for the table doa_sub_categories.
type DoaSubCategoriesColumns struct {
	Id          string //
	ParentId    string //
	Name        string //
	Slug        string //
	Description string //
	OrderNumber string //
	ViewCount   string //
	ParentName  string //
	Total       string //
}

// doaSubCategoriesColumns holds the columns for the table doa_sub_categories.
var doaSubCategoriesColumns = DoaSubCategoriesColumns{
	Id:          "id",
	ParentId:    "parent_id",
	Name:        "name",
	Slug:        "slug",
	Description: "description",
	OrderNumber: "order_number",
	ViewCount:   "view_count",
	ParentName:  "parent_name",
	Total:       "total",
}

// NewDoaSubCategoriesDao creates and returns a new DAO object for table data access.
func NewDoaSubCategoriesDao(handlers ...gdb.ModelHandler) *DoaSubCategoriesDao {
	return &DoaSubCategoriesDao{
		group:    "default",
		table:    "doa_sub_categories",
		columns:  doaSubCategoriesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DoaSubCategoriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DoaSubCategoriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DoaSubCategoriesDao) Columns() DoaSubCategoriesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DoaSubCategoriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DoaSubCategoriesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DoaSubCategoriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
