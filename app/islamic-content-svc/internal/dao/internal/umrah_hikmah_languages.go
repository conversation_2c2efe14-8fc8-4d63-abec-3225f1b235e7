// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahHikmahLanguagesDao is the data access object for the table umrah_hikmah_languages.
type UmrahHikmahLanguagesDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  UmrahHikmahLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// UmrahHikmahLanguagesColumns defines and stores column names for the table umrah_hikmah_languages.
type UmrahHikmahLanguagesColumns struct {
	Id         string // 主键ID
	HikmahId   string // 副朝智慧ID，关联umrah_hikmah.id
	LanguageId string // 语言ID：0-中文，1-英文，2-印尼语
	Title      string // 标题
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// umrahHikmahLanguagesColumns holds the columns for the table umrah_hikmah_languages.
var umrahHikmahLanguagesColumns = UmrahHikmahLanguagesColumns{
	Id:         "id",
	HikmahId:   "hikmah_id",
	LanguageId: "language_id",
	Title:      "title",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUmrahHikmahLanguagesDao creates and returns a new DAO object for table data access.
func NewUmrahHikmahLanguagesDao(handlers ...gdb.ModelHandler) *UmrahHikmahLanguagesDao {
	return &UmrahHikmahLanguagesDao{
		group:    "default",
		table:    "umrah_hikmah_languages",
		columns:  umrahHikmahLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahHikmahLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahHikmahLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahHikmahLanguagesDao) Columns() UmrahHikmahLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahHikmahLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahHikmahLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahHikmahLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
