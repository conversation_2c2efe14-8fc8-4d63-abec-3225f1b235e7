// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DoaCategoriesDao is the data access object for the table doa_categories.
type DoaCategoriesDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  DoaCategoriesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// DoaCategoriesColumns defines and stores column names for the table doa_categories.
type DoaCategoriesColumns struct {
	Id          string //
	Name        string //
	Slug        string //
	OrderNumber string //
	ViewCount   string //
	GroupName   string //
	Total       string //
}

// doaCategoriesColumns holds the columns for the table doa_categories.
var doaCategoriesColumns = DoaCategoriesColumns{
	Id:          "id",
	Name:        "name",
	Slug:        "slug",
	OrderNumber: "order_number",
	ViewCount:   "view_count",
	GroupName:   "group_name",
	Total:       "total",
}

// NewDoaCategoriesDao creates and returns a new DAO object for table data access.
func NewDoaCategoriesDao(handlers ...gdb.ModelHandler) *DoaCategoriesDao {
	return &DoaCategoriesDao{
		group:    "default",
		table:    "doa_categories",
		columns:  doaCategoriesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DoaCategoriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DoaCategoriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DoaCategoriesDao) Columns() DoaCategoriesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DoaCategoriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DoaCategoriesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DoaCategoriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
