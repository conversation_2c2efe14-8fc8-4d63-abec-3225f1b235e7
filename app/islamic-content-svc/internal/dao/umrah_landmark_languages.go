// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// umrahLandmarkLanguagesDao is the data access object for the table umrah_landmark_languages.
// You can define custom methods on it to extend its functionality as needed.
type umrahLandmarkLanguagesDao struct {
	*internal.UmrahLandmarkLanguagesDao
}

var (
	// UmrahLandmarkLanguages is a globally accessible object for table umrah_landmark_languages operations.
	UmrahLandmarkLanguages = umrahLandmarkLanguagesDao{internal.NewUmrahLandmarkLanguagesDao()}
)

// Add your custom methods and functionality below.
