// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// umrahDoaRingkasDao is the data access object for the table umrah_doa_ringkas.
// You can define custom methods on it to extend its functionality as needed.
type umrahDoaRingkasDao struct {
	*internal.UmrahDoaRingkasDao
}

var (
	// UmrahDoaRingkas is a globally accessible object for table umrah_doa_ringkas operations.
	UmrahDoaRingkas = umrahDoaRingkasDao{internal.NewUmrahDoaRingkasDao()}
)

// Add your custom methods and functionality below.
