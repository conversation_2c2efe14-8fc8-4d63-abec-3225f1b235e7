// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// advertisementLanguagesDao is the data access object for the table advertisement_languages.
// You can define custom methods on it to extend its functionality as needed.
type advertisementLanguagesDao struct {
	*internal.AdvertisementLanguagesDao
}

var (
	// AdvertisementLanguages is a globally accessible object for table advertisement_languages operations.
	AdvertisementLanguages = advertisementLanguagesDao{internal.NewAdvertisementLanguagesDao()}
)

// Add your custom methods and functionality below.
