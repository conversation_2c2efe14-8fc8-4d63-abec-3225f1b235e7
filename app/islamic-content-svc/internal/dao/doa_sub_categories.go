// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// doaSubCategoriesDao is the data access object for the table doa_sub_categories.
// You can define custom methods on it to extend its functionality as needed.
type doaSubCategoriesDao struct {
	*internal.DoaSubCategoriesDao
}

var (
	// DoaSubCategories is a globally accessible object for table doa_sub_categories operations.
	DoaSubCategories = doaSubCategoriesDao{internal.NewDoaSubCategoriesDao()}
)

// Add your custom methods and functionality below.
