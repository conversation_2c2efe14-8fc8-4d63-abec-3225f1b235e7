// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// doaDataDao is the data access object for the table doa_data.
// You can define custom methods on it to extend its functionality as needed.
type doaDataDao struct {
	*internal.DoaDataDao
}

var (
	// DoaData is a globally accessible object for table doa_data operations.
	DoaData = doaDataDao{internal.NewDoaDataDao()}
)

// Add your custom methods and functionality below.
