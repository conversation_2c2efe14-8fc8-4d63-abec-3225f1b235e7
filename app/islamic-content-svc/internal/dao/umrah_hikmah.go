// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// umrahHikmahDao is the data access object for the table umrah_hikmah.
// You can define custom methods on it to extend its functionality as needed.
type umrahHikmahDao struct {
	*internal.UmrahHikmahDao
}

var (
	// UmrahHikmah is a globally accessible object for table umrah_hikmah operations.
	UmrahHikmah = umrahHikmahDao{internal.NewUmrahHikmahDao()}
)

// Add your custom methods and functionality below.
