// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// surahShareDao is the data access object for the table surah_share.
// You can define custom methods on it to extend its functionality as needed.
type surahShareDao struct {
	*internal.SurahShareDao
}

var (
	// SurahShare is a globally accessible object for table surah_share operations.
	SurahShare = surahShareDao{internal.NewSurahShareDao()}
)

// Add your custom methods and functionality below.
