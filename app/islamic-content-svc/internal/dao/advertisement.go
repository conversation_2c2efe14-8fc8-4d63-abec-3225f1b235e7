// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// advertisementDao is the data access object for the table advertisement.
// You can define custom methods on it to extend its functionality as needed.
type advertisementDao struct {
	*internal.AdvertisementDao
}

var (
	// Advertisement is a globally accessible object for table advertisement operations.
	Advertisement = advertisementDao{internal.NewAdvertisementDao()}
)

// Add your custom methods and functionality below.
