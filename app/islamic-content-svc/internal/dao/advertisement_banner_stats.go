// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// advertisementBannerStatsDao is the data access object for the table advertisement_banner_stats.
// You can define custom methods on it to extend its functionality as needed.
type advertisementBannerStatsDao struct {
	*internal.AdvertisementBannerStatsDao
}

var (
	// AdvertisementBannerStats is a globally accessible object for table advertisement_banner_stats operations.
	AdvertisementBannerStats = advertisementBannerStatsDao{internal.NewAdvertisementBannerStatsDao()}
)

// Add your custom methods and functionality below.
