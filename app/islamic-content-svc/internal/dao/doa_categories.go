// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// doaCategoriesDao is the data access object for the table doa_categories.
// You can define custom methods on it to extend its functionality as needed.
type doaCategoriesDao struct {
	*internal.DoaCategoriesDao
}

var (
	// DoaCategories is a globally accessible object for table doa_categories operations.
	DoaCategories = doaCategoriesDao{internal.NewDoaCategoriesDao()}
)

// Add your custom methods and functionality below.
