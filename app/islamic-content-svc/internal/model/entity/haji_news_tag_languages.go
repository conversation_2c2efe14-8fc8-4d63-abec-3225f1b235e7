// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// HajiNewsTagLanguages is the golang structure for table haji_news_tag_languages.
type HajiNewsTagLanguages struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`                    // 主键ID
	TagId      uint64 `json:"tagId"      orm:"tag_id"      description:"标签ID，关联haji_news_tag.id"` // 标签ID，关联haji_news_tag.id
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"`    // 语言ID：0-中文，1-英文，2-印尼语
	TagName    string `json:"tagName"    orm:"tag_name"    description:"标签名称"`                    // 标签名称
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`             // 创建时间（毫秒时间戳）
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`             // 更新时间（毫秒时间戳）
}
