// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// DoaData is the golang structure for table doa_data.
type DoaData struct {
	Id              int    `json:"id"              orm:"id"              description:""` //
	CategoryId      int    `json:"categoryId"      orm:"category_id"     description:""` //
	Arabic          string `json:"arabic"          orm:"arabic"          description:""` //
	Translate       string `json:"translate"       orm:"translate"       description:""` //
	Transliteration string `json:"transliteration" orm:"transliteration" description:""` //
	Type            int    `json:"type"            orm:"type"            description:""` //
	OrderNumber     int    `json:"orderNumber"     orm:"order_number"    description:""` //
}
