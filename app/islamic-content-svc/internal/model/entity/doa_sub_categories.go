// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// DoaSubCategories is the golang structure for table doa_sub_categories.
type DoaSubCategories struct {
	Id          int    `json:"id"          orm:"id"           description:""` //
	ParentId    int    `json:"parentId"    orm:"parent_id"    description:""` //
	Name        string `json:"name"        orm:"name"         description:""` //
	Slug        string `json:"slug"        orm:"slug"         description:""` //
	Description string `json:"description" orm:"description"  description:""` //
	OrderNumber int    `json:"orderNumber" orm:"order_number" description:""` //
	ViewCount   int    `json:"viewCount"   orm:"view_count"   description:""` //
	ParentName  string `json:"parentName"  orm:"parent_name"  description:""` //
	Total       int    `json:"total"       orm:"total"        description:""` //
}
