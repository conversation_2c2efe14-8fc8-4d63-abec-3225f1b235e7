// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UmrahDoaPanjang is the golang structure for table umrah_doa_panjang.
type UmrahDoaPanjang struct {
	Id          uint64 `json:"id"          orm:"id"           description:"主键ID"`        // 主键ID
	DoaNo       int    `json:"doaNo"       orm:"doa_no"       description:"祈祷文序号"`       // 祈祷文序号
	DoaName     string `json:"doaName"     orm:"doa_name"     description:"祈祷文名称"`       // 祈祷文名称
	BacaanCount int    `json:"bacaanCount" orm:"bacaan_count" description:"诵读数"`         // 诵读数
	CreateTime  uint64 `json:"createTime"  orm:"create_time"  description:"创建时间（毫秒时间戳）"` // 创建时间（毫秒时间戳）
	UpdateTime  uint64 `json:"updateTime"  orm:"update_time"  description:"更新时间（毫秒时间戳）"` // 更新时间（毫秒时间戳）
}
