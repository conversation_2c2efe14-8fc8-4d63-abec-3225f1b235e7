// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// DoaCategories is the golang structure for table doa_categories.
type DoaCategories struct {
	Id          int    `json:"id"          orm:"id"           description:""` //
	Name        string `json:"name"        orm:"name"         description:""` //
	Slug        string `json:"slug"        orm:"slug"         description:""` //
	OrderNumber int    `json:"orderNumber" orm:"order_number" description:""` //
	ViewCount   int    `json:"viewCount"   orm:"view_count"   description:""` //
	GroupName   string `json:"groupName"   orm:"group_name"   description:""` //
	Total       int    `json:"total"       orm:"total"        description:""` //
}
