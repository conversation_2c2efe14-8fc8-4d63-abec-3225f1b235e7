// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UmrahLandmark is the golang structure for table umrah_landmark.
type UmrahLandmark struct {
	Id         uint64  `json:"id"         orm:"id"          description:"主键ID"`                            // 主键ID
	TypeId     uint64  `json:"typeId"     orm:"type_id"     description:"地标类型ID，关联umrah_landmark_type.id"` // 地标类型ID，关联umrah_landmark_type.id
	InnerType  string  `json:"innerType"  orm:"inner_type"  description:"内部类型: (destinasi, tokoh)"`        // 内部类型: (destinasi, tokoh)
	Latitude   float64 `json:"latitude"   orm:"latitude"    description:"纬度"`                              // 纬度
	Longitude  float64 `json:"longitude"  orm:"longitude"   description:"经度"`                              // 经度
	ImageUrl   string  `json:"imageUrl"   orm:"image_url"   description:"图片URL"`                           // 图片URL
	SortOrder  uint    `json:"sortOrder"  orm:"sort_order"  description:"排序值，数字越小排序越靠前"`                   // 排序值，数字越小排序越靠前
	CreateTime uint64  `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`                     // 创建时间（毫秒时间戳）
	UpdateTime uint64  `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`                     // 更新时间（毫秒时间戳）
}
