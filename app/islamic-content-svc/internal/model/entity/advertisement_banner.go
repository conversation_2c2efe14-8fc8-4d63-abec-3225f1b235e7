// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// AdvertisementBanner is the golang structure for table advertisement_banner.
type AdvertisementBanner struct {
	Id              uint   `json:"id"              orm:"id"               description:"主键ID"`                    // 主键ID
	AdvertisementId uint   `json:"advertisementId" orm:"advertisement_id" description:"广告ID"`                    // 广告ID
	LanguageId      uint   `json:"languageId"      orm:"language_id"      description:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	ImageUrl        string `json:"imageUrl"        orm:"image_url"        description:"Banner图片URL"`             // Banner图片URL
	LinkUrl         string `json:"linkUrl"         orm:"link_url"         description:"跳转链接URL"`                 // 跳转链接URL
	SortOrder       uint   `json:"sortOrder"       orm:"sort_order"       description:"图片排序权重，数字越小越靠前"`          // 图片排序权重，数字越小越靠前
	CreateTime      uint64 `json:"createTime"      orm:"create_time"      description:"创建时间(毫秒时间戳)"`             // 创建时间(毫秒时间戳)
	UpdateTime      uint64 `json:"updateTime"      orm:"update_time"      description:"更新时间(毫秒时间戳)"`             // 更新时间(毫秒时间戳)
}
