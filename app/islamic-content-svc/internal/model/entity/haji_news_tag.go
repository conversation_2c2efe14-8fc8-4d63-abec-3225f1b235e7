// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// HajiNewsTag is the golang structure for table haji_news_tag.
type HajiNewsTag struct {
	Id           uint64 `json:"id"           orm:"id"            description:"主键ID"`                       // 主键ID
	ArticleCount uint   `json:"articleCount" orm:"article_count" description:"关联文章数（不一定使用这个字段，可由关联表实时计算）"` // 关联文章数（不一定使用这个字段，可由关联表实时计算）
	SortOrder    uint   `json:"sortOrder"    orm:"sort_order"    description:"排序值，数字越小排序越靠前"`              // 排序值，数字越小排序越靠前
	CreateTime   uint64 `json:"createTime"   orm:"create_time"   description:"创建时间（毫秒时间戳）"`                // 创建时间（毫秒时间戳）
	UpdateTime   uint64 `json:"updateTime"   orm:"update_time"   description:"更新时间（毫秒时间戳）"`                // 更新时间（毫秒时间戳）
}
