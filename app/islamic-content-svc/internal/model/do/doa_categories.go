// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DoaCategories is the golang structure of table doa_categories for DAO operations like Where/Data.
type DoaCategories struct {
	g.Meta      `orm:"table:doa_categories, do:true"`
	Id          interface{} //
	Name        interface{} //
	Slug        interface{} //
	OrderNumber interface{} //
	ViewCount   interface{} //
	GroupName   interface{} //
	Total       interface{} //
}
