// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiNewsTag is the golang structure of table haji_news_tag for DAO operations like Where/Data.
type HajiNewsTag struct {
	g.Meta       `orm:"table:haji_news_tag, do:true"`
	Id           interface{} // 主键ID
	ArticleCount interface{} // 关联文章数（不一定使用这个字段，可由关联表实时计算）
	SortOrder    interface{} // 排序值，数字越小排序越靠前
	CreateTime   interface{} // 创建时间（毫秒时间戳）
	UpdateTime   interface{} // 更新时间（毫秒时间戳）
}
