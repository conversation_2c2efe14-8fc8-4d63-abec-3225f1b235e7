// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementBanner is the golang structure of table advertisement_banner for DAO operations like Where/Data.
type AdvertisementBanner struct {
	g.Meta          `orm:"table:advertisement_banner, do:true"`
	Id              interface{} // 主键ID
	AdvertisementId interface{} // 广告ID
	LanguageId      interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	ImageUrl        interface{} // Banner图片URL
	LinkUrl         interface{} // 跳转链接URL
	SortOrder       interface{} // 图片排序权重，数字越小越靠前
	CreateTime      interface{} // 创建时间(毫秒时间戳)
	UpdateTime      interface{} // 更新时间(毫秒时间戳)
}
