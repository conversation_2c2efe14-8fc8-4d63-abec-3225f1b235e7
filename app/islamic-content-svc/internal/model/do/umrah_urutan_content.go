// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahUrutanContent is the golang structure of table umrah_urutan_content for DAO operations like Where/Data.
type UmrahUrutanContent struct {
	g.Meta        `orm:"table:umrah_urutan_content, do:true"`
	Id            interface{} // 主键ID
	UrutanId      interface{} // 副朝顺序ID，关联umrah_urutan.id
	LanguageId    interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	UrutanName    interface{} // 副朝仪式名称（最多60个字符）
	UrutanTime    interface{} // 仪式时间
	UrutanContent interface{} // 仪式内容描述（富文本）
	CreateTime    interface{} // 创建时间（毫秒时间戳）
	UpdateTime    interface{} // 更新时间（毫秒时间戳）
}
