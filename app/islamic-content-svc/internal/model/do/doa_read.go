// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DoaRead is the golang structure of table doa_read for DAO operations like Where/Data.
type DoaRead struct {
	g.Meta     `orm:"table:doa_read, do:true"`
	Id         interface{} //
	UserId     interface{} // 用户id
	Types      interface{} // 类型 1-doa,2-wirid- 废弃
	PId        interface{} // 父级id
	BaccanId   interface{} // baccan_id-sub-cate-id
	PName      interface{} // 父级名称
	BaccanName interface{} // 名称
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未更新
}
