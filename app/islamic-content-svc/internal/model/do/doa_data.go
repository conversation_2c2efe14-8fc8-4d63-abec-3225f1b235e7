// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DoaData is the golang structure of table doa_data for DAO operations like Where/Data.
type DoaData struct {
	g.Meta          `orm:"table:doa_data, do:true"`
	Id              interface{} //
	CategoryId      interface{} //
	Arabic          interface{} //
	Translate       interface{} //
	Transliteration interface{} //
	Type            interface{} //
	OrderNumber     interface{} //
}
