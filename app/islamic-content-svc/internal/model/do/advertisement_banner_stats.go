// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementBannerStats is the golang structure of table advertisement_banner_stats for DAO operations like Where/Data.
type AdvertisementBannerStats struct {
	g.Meta          `orm:"table:advertisement_banner_stats, do:true"`
	Id              interface{} // 主键ID
	BannerId        interface{} // 广告ID
	AdvertisementId interface{} // 广告ID
	LanguageId      interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	UserId          interface{} // 用户ID，0表示未登录用户
	ActionType      interface{} // 操作类型(click/view)
	DeviceId        interface{} // 设备唯一标识
	IpAddress       interface{} // IP地址
	UserAgent       interface{} // 用户代理信息
	CreateTime      interface{} // 操作时间(毫秒时间戳)
}
