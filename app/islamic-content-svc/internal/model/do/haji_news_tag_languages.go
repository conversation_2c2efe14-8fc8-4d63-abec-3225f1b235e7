// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiNewsTagLanguages is the golang structure of table haji_news_tag_languages for DAO operations like Where/Data.
type HajiNewsTagLanguages struct {
	g.Meta     `orm:"table:haji_news_tag_languages, do:true"`
	Id         interface{} // 主键ID
	TagId      interface{} // 标签ID，关联haji_news_tag.id
	LanguageId interface{} // 语言ID：0-中文，1-英文，2-印尼语
	TagName    interface{} // 标签名称
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
