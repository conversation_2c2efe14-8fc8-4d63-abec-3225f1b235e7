// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementPosition is the golang structure of table advertisement_position for DAO operations like Where/Data.
type AdvertisementPosition struct {
	g.Meta       `orm:"table:advertisement_position, do:true"`
	Id           interface{} // 主键ID
	PositionName interface{} // 广告位名称，不可修改
	PositionCode interface{} // 位置编码，自动生成，不可修改
	Remark       interface{} // 备注，对该广告位置的备注，可修改
	CreateTime   interface{} // 创建时间(毫秒时间戳)
	UpdateTime   interface{} // 更新时间(毫秒时间戳)
}
