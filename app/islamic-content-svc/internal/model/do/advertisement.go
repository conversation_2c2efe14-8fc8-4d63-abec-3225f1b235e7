// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Advertisement is the golang structure of table advertisement for DAO operations like Where/Data.
type Advertisement struct {
	g.Meta       `orm:"table:advertisement, do:true"`
	Id           interface{} // 主键ID
	PositionCode interface{} // 广告位置编码
	SortOrder    interface{} // 排序权重，数字越小越靠前
	Status       interface{} // 状态: 0-禁用, 1-启用
	StartTime    interface{} // 开始时间戳(毫秒)
	EndTime      interface{} // 结束时间戳(毫秒)
	AdminId      interface{} // 创建管理员ID
	CreateTime   interface{} // 创建时间(毫秒时间戳)
	UpdateTime   interface{} // 更新时间(毫秒时间戳)
}
