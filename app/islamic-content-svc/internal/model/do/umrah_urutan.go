// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahUrutan is the golang structure of table umrah_urutan for DAO operations like Where/Data.
type UmrahUrutan struct {
	g.Meta     `orm:"table:umrah_urutan, do:true"`
	Id         interface{} // 主键ID
	UrutanNo   interface{} // 副朝顺序（数字）
	IconUrl    interface{} // 图标URL
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
