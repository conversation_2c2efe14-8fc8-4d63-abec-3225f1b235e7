// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DoaSubCategories is the golang structure of table doa_sub_categories for DAO operations like Where/Data.
type DoaSubCategories struct {
	g.Meta      `orm:"table:doa_sub_categories, do:true"`
	Id          interface{} //
	ParentId    interface{} //
	Name        interface{} //
	Slug        interface{} //
	Description interface{} //
	OrderNumber interface{} //
	ViewCount   interface{} //
	ParentName  interface{} //
	Total       interface{} //
}
