// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaPanjang is the golang structure of table umrah_doa_panjang for DAO operations like Where/Data.
type UmrahDoaPanjang struct {
	g.Meta      `orm:"table:umrah_doa_panjang, do:true"`
	Id          interface{} // 主键ID
	DoaNo       interface{} // 祈祷文序号
	DoaName     interface{} // 祈祷文名称
	BacaanCount interface{} // 诵读数
	CreateTime  interface{} // 创建时间（毫秒时间戳）
	UpdateTime  interface{} // 更新时间（毫秒时间戳）
}
