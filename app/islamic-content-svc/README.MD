# 5. 宗教&内容服务（islamic-content-svc）

负责：古兰经、祷告、礼拜、朝觐、资讯、头条、专题、视频、日历

**典型接口：**
- 古兰经章节/音频/注释
- 祷告/礼拜时间与提醒
- 朝觐相关信息/证书
- 资讯/专题/视频内容
- 日历/提醒/推送
- test

## 接口缓存记录

* Islamic服务的全部GET请求，强缓存60s，缓存过期后，如果源站报错，app还能继续用
* POST请求不缓存，用户相关数据需要post请求

### 不可缓存接口记录

* 接口/api/islamic-content/*，默认是GET请求，只有下面的接口使用POST

```
/api/islamic-content/islamic/v1/SurahService
    AyahReadRecord
    AyahReadCollectList
    AyahShare
    AyahReadCollect
    CheckAyahReadCollectStatus
    AyahReadRecordList
    NewsCollectArticleIds
    NewsCollectStatusCheck
    NewsCollectOp
    NewsViewOp
    NewsShareOp
    DoaReadCollectList
    DoaReadCollect
    DoaRead
    CheckDoaReadCollectStatus
    DoaCollectIds
    AyahCollectIds

/api/islamic-content/islamic/v1/VideoService/
    VideoDetail
    VideoCollect
    VideoCollectList
    VideoCollectStatusCheck


/api/islamic-content/islamic/v1/FeedbackService/FeedbackAdd
/api/islamic-content/islamic/v1/BannerService/BannerClick

FIXME: 所有调用batchCheckDoaCollectStatus()的方法的接口，携带了用户数据，暂时都被设置成共享缓存（用户数据错乱）
```

