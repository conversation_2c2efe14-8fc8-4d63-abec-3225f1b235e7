// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/doa_data.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DoaData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                          //
	CategoryId      int32                  `protobuf:"varint,2,opt,name=CategoryId,proto3" json:"CategoryId,omitempty"`          //
	Arabic          string                 `protobuf:"bytes,3,opt,name=Arabic,proto3" json:"Arabic,omitempty"`                   //
	Translate       string                 `protobuf:"bytes,4,opt,name=Translate,proto3" json:"Translate,omitempty"`             //
	Transliteration string                 `protobuf:"bytes,5,opt,name=Transliteration,proto3" json:"Transliteration,omitempty"` //
	Type            int32                  `protobuf:"varint,6,opt,name=Type,proto3" json:"Type,omitempty"`                      //
	OrderNumber     int32                  `protobuf:"varint,7,opt,name=OrderNumber,proto3" json:"OrderNumber,omitempty"`        //
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DoaData) Reset() {
	*x = DoaData{}
	mi := &file_pbentity_doa_data_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaData) ProtoMessage() {}

func (x *DoaData) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_doa_data_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaData.ProtoReflect.Descriptor instead.
func (*DoaData) Descriptor() ([]byte, []int) {
	return file_pbentity_doa_data_proto_rawDescGZIP(), []int{0}
}

func (x *DoaData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaData) GetCategoryId() int32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *DoaData) GetArabic() string {
	if x != nil {
		return x.Arabic
	}
	return ""
}

func (x *DoaData) GetTranslate() string {
	if x != nil {
		return x.Translate
	}
	return ""
}

func (x *DoaData) GetTransliteration() string {
	if x != nil {
		return x.Transliteration
	}
	return ""
}

func (x *DoaData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DoaData) GetOrderNumber() int32 {
	if x != nil {
		return x.OrderNumber
	}
	return 0
}

var File_pbentity_doa_data_proto protoreflect.FileDescriptor

const file_pbentity_doa_data_proto_rawDesc = "" +
	"\n" +
	"\x17pbentity/doa_data.proto\x12\bpbentity\"\xcf\x01\n" +
	"\aDoaData\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x1e\n" +
	"\n" +
	"CategoryId\x18\x02 \x01(\x05R\n" +
	"CategoryId\x12\x16\n" +
	"\x06Arabic\x18\x03 \x01(\tR\x06Arabic\x12\x1c\n" +
	"\tTranslate\x18\x04 \x01(\tR\tTranslate\x12(\n" +
	"\x0fTransliteration\x18\x05 \x01(\tR\x0fTransliteration\x12\x12\n" +
	"\x04Type\x18\x06 \x01(\x05R\x04Type\x12 \n" +
	"\vOrderNumber\x18\a \x01(\x05R\vOrderNumberB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_doa_data_proto_rawDescOnce sync.Once
	file_pbentity_doa_data_proto_rawDescData []byte
)

func file_pbentity_doa_data_proto_rawDescGZIP() []byte {
	file_pbentity_doa_data_proto_rawDescOnce.Do(func() {
		file_pbentity_doa_data_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_doa_data_proto_rawDesc), len(file_pbentity_doa_data_proto_rawDesc)))
	})
	return file_pbentity_doa_data_proto_rawDescData
}

var file_pbentity_doa_data_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_doa_data_proto_goTypes = []any{
	(*DoaData)(nil), // 0: pbentity.DoaData
}
var file_pbentity_doa_data_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_doa_data_proto_init() }
func file_pbentity_doa_data_proto_init() {
	if File_pbentity_doa_data_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_doa_data_proto_rawDesc), len(file_pbentity_doa_data_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_doa_data_proto_goTypes,
		DependencyIndexes: file_pbentity_doa_data_proto_depIdxs,
		MessageInfos:      file_pbentity_doa_data_proto_msgTypes,
	}.Build()
	File_pbentity_doa_data_proto = out.File
	file_pbentity_doa_data_proto_goTypes = nil
	file_pbentity_doa_data_proto_depIdxs = nil
}
