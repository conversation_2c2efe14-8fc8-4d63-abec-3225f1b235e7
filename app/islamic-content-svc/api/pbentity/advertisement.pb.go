// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/advertisement.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Advertisement struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                        // 主键ID
	PositionCode  string                 `protobuf:"bytes,2,opt,name=PositionCode,proto3" json:"PositionCode,omitempty" dc:"广告位置编码"`   // 广告位置编码
	SortOrder     uint32                 `protobuf:"varint,3,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"`  // 排序权重，数字越小越靠前
	Status        uint32                 `protobuf:"varint,4,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态: 0-禁用, 1-启用"`      // 状态: 0-禁用, 1-启用
	StartTime     uint64                 `protobuf:"varint,5,opt,name=StartTime,proto3" json:"StartTime,omitempty" dc:"开始时间戳(毫秒)"`     // 开始时间戳(毫秒)
	EndTime       uint64                 `protobuf:"varint,6,opt,name=EndTime,proto3" json:"EndTime,omitempty" dc:"结束时间戳(毫秒)"`         // 结束时间戳(毫秒)
	AdminId       uint32                 `protobuf:"varint,7,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"创建管理员ID"`           // 创建管理员ID
	CreateTime    uint64                 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"` // 创建时间(毫秒时间戳)
	UpdateTime    uint64                 `protobuf:"varint,9,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"` // 更新时间(毫秒时间戳)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Advertisement) Reset() {
	*x = Advertisement{}
	mi := &file_pbentity_advertisement_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Advertisement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Advertisement) ProtoMessage() {}

func (x *Advertisement) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_advertisement_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Advertisement.ProtoReflect.Descriptor instead.
func (*Advertisement) Descriptor() ([]byte, []int) {
	return file_pbentity_advertisement_proto_rawDescGZIP(), []int{0}
}

func (x *Advertisement) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Advertisement) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

func (x *Advertisement) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Advertisement) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Advertisement) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Advertisement) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Advertisement) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *Advertisement) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Advertisement) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_advertisement_proto protoreflect.FileDescriptor

const file_pbentity_advertisement_proto_rawDesc = "" +
	"\n" +
	"\x1cpbentity/advertisement.proto\x12\bpbentity\"\x8b\x02\n" +
	"\rAdvertisement\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\"\n" +
	"\fPositionCode\x18\x02 \x01(\tR\fPositionCode\x12\x1c\n" +
	"\tSortOrder\x18\x03 \x01(\rR\tSortOrder\x12\x16\n" +
	"\x06Status\x18\x04 \x01(\rR\x06Status\x12\x1c\n" +
	"\tStartTime\x18\x05 \x01(\x04R\tStartTime\x12\x18\n" +
	"\aEndTime\x18\x06 \x01(\x04R\aEndTime\x12\x18\n" +
	"\aAdminId\x18\a \x01(\rR\aAdminId\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\b \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\t \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_advertisement_proto_rawDescOnce sync.Once
	file_pbentity_advertisement_proto_rawDescData []byte
)

func file_pbentity_advertisement_proto_rawDescGZIP() []byte {
	file_pbentity_advertisement_proto_rawDescOnce.Do(func() {
		file_pbentity_advertisement_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_proto_rawDesc), len(file_pbentity_advertisement_proto_rawDesc)))
	})
	return file_pbentity_advertisement_proto_rawDescData
}

var file_pbentity_advertisement_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_advertisement_proto_goTypes = []any{
	(*Advertisement)(nil), // 0: pbentity.Advertisement
}
var file_pbentity_advertisement_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_advertisement_proto_init() }
func file_pbentity_advertisement_proto_init() {
	if File_pbentity_advertisement_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_proto_rawDesc), len(file_pbentity_advertisement_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_advertisement_proto_goTypes,
		DependencyIndexes: file_pbentity_advertisement_proto_depIdxs,
		MessageInfos:      file_pbentity_advertisement_proto_msgTypes,
	}.Build()
	File_pbentity_advertisement_proto = out.File
	file_pbentity_advertisement_proto_goTypes = nil
	file_pbentity_advertisement_proto_depIdxs = nil
}
