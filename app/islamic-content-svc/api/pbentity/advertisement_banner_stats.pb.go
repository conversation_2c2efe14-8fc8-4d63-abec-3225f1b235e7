// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/advertisement_banner_stats.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdvertisementBannerStats struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	BannerId        uint32                 `protobuf:"varint,2,opt,name=BannerId,proto3" json:"BannerId,omitempty" dc:"广告ID"`                        // 广告ID
	AdvertisementId uint32                 `protobuf:"varint,3,opt,name=AdvertisementId,proto3" json:"AdvertisementId,omitempty" dc:"广告ID"`          // 广告ID
	LanguageId      uint32                 `protobuf:"varint,4,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	UserId          uint64                 `protobuf:"varint,5,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户ID，0表示未登录用户"`                   // 用户ID，0表示未登录用户
	ActionType      string                 `protobuf:"bytes,6,opt,name=ActionType,proto3" json:"ActionType,omitempty" dc:"操作类型(click/view)"`         // 操作类型(click/view)
	DeviceId        string                 `protobuf:"bytes,7,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"设备唯一标识"`                       // 设备唯一标识
	IpAddress       string                 `protobuf:"bytes,8,opt,name=IpAddress,proto3" json:"IpAddress,omitempty" dc:"IP地址"`                       // IP地址
	UserAgent       string                 `protobuf:"bytes,9,opt,name=UserAgent,proto3" json:"UserAgent,omitempty" dc:"用户代理信息"`                     // 用户代理信息
	CreateTime      uint64                 `protobuf:"varint,10,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"操作时间(毫秒时间戳)"`            // 操作时间(毫秒时间戳)
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AdvertisementBannerStats) Reset() {
	*x = AdvertisementBannerStats{}
	mi := &file_pbentity_advertisement_banner_stats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvertisementBannerStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvertisementBannerStats) ProtoMessage() {}

func (x *AdvertisementBannerStats) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_advertisement_banner_stats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvertisementBannerStats.ProtoReflect.Descriptor instead.
func (*AdvertisementBannerStats) Descriptor() ([]byte, []int) {
	return file_pbentity_advertisement_banner_stats_proto_rawDescGZIP(), []int{0}
}

func (x *AdvertisementBannerStats) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AdvertisementBannerStats) GetBannerId() uint32 {
	if x != nil {
		return x.BannerId
	}
	return 0
}

func (x *AdvertisementBannerStats) GetAdvertisementId() uint32 {
	if x != nil {
		return x.AdvertisementId
	}
	return 0
}

func (x *AdvertisementBannerStats) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *AdvertisementBannerStats) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AdvertisementBannerStats) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *AdvertisementBannerStats) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AdvertisementBannerStats) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *AdvertisementBannerStats) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *AdvertisementBannerStats) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

var File_pbentity_advertisement_banner_stats_proto protoreflect.FileDescriptor

const file_pbentity_advertisement_banner_stats_proto_rawDesc = "" +
	"\n" +
	")pbentity/advertisement_banner_stats.proto\x12\bpbentity\"\xc0\x02\n" +
	"\x18AdvertisementBannerStats\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1a\n" +
	"\bBannerId\x18\x02 \x01(\rR\bBannerId\x12(\n" +
	"\x0fAdvertisementId\x18\x03 \x01(\rR\x0fAdvertisementId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x04 \x01(\rR\n" +
	"LanguageId\x12\x16\n" +
	"\x06UserId\x18\x05 \x01(\x04R\x06UserId\x12\x1e\n" +
	"\n" +
	"ActionType\x18\x06 \x01(\tR\n" +
	"ActionType\x12\x1a\n" +
	"\bDeviceId\x18\a \x01(\tR\bDeviceId\x12\x1c\n" +
	"\tIpAddress\x18\b \x01(\tR\tIpAddress\x12\x1c\n" +
	"\tUserAgent\x18\t \x01(\tR\tUserAgent\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\n" +
	" \x01(\x04R\n" +
	"CreateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_advertisement_banner_stats_proto_rawDescOnce sync.Once
	file_pbentity_advertisement_banner_stats_proto_rawDescData []byte
)

func file_pbentity_advertisement_banner_stats_proto_rawDescGZIP() []byte {
	file_pbentity_advertisement_banner_stats_proto_rawDescOnce.Do(func() {
		file_pbentity_advertisement_banner_stats_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_banner_stats_proto_rawDesc), len(file_pbentity_advertisement_banner_stats_proto_rawDesc)))
	})
	return file_pbentity_advertisement_banner_stats_proto_rawDescData
}

var file_pbentity_advertisement_banner_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_advertisement_banner_stats_proto_goTypes = []any{
	(*AdvertisementBannerStats)(nil), // 0: pbentity.AdvertisementBannerStats
}
var file_pbentity_advertisement_banner_stats_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_advertisement_banner_stats_proto_init() }
func file_pbentity_advertisement_banner_stats_proto_init() {
	if File_pbentity_advertisement_banner_stats_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_banner_stats_proto_rawDesc), len(file_pbentity_advertisement_banner_stats_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_advertisement_banner_stats_proto_goTypes,
		DependencyIndexes: file_pbentity_advertisement_banner_stats_proto_depIdxs,
		MessageInfos:      file_pbentity_advertisement_banner_stats_proto_msgTypes,
	}.Build()
	File_pbentity_advertisement_banner_stats_proto = out.File
	file_pbentity_advertisement_banner_stats_proto_goTypes = nil
	file_pbentity_advertisement_banner_stats_proto_depIdxs = nil
}
