// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/surat_tafsir.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SuratTafsir struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                  //
	TafsirId    int32  `protobuf:"varint,2,opt,name=TafsirId,proto3" json:"TafsirId,omitempty" dc:"注释全局ID"`          // 注释全局ID
	SurahId     int32  `protobuf:"varint,3,opt,name=SurahId,proto3" json:"SurahId,omitempty" dc:"所属章节ID"`            // 所属章节ID
	AyatNomor   int32  `protobuf:"varint,4,opt,name=AyatNomor,proto3" json:"AyatNomor,omitempty" dc:"对应经文编号"`        // 对应经文编号
	Tafsir      string `protobuf:"bytes,5,opt,name=Tafsir,proto3" json:"Tafsir,omitempty" dc:"注释内容"`                 // 注释内容
	CreatedTime uint64 `protobuf:"varint,6,opt,name=CreatedTime,proto3" json:"CreatedTime,omitempty" dc:"创建时间戳(毫秒)"` // 创建时间戳(毫秒)
	UpdatedTime uint64 `protobuf:"varint,7,opt,name=UpdatedTime,proto3" json:"UpdatedTime,omitempty" dc:"修改时间戳(毫秒)"` // 修改时间戳(毫秒)
	Wajiz       string `protobuf:"bytes,8,opt,name=Wajiz,proto3" json:"Wajiz,omitempty" dc:"wajiz 解释"`               // wajiz 解释
}

func (x *SuratTafsir) Reset() {
	*x = SuratTafsir{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_surat_tafsir_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuratTafsir) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratTafsir) ProtoMessage() {}

func (x *SuratTafsir) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surat_tafsir_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratTafsir.ProtoReflect.Descriptor instead.
func (*SuratTafsir) Descriptor() ([]byte, []int) {
	return file_pbentity_surat_tafsir_proto_rawDescGZIP(), []int{0}
}

func (x *SuratTafsir) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratTafsir) GetTafsirId() int32 {
	if x != nil {
		return x.TafsirId
	}
	return 0
}

func (x *SuratTafsir) GetSurahId() int32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *SuratTafsir) GetAyatNomor() int32 {
	if x != nil {
		return x.AyatNomor
	}
	return 0
}

func (x *SuratTafsir) GetTafsir() string {
	if x != nil {
		return x.Tafsir
	}
	return ""
}

func (x *SuratTafsir) GetCreatedTime() uint64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *SuratTafsir) GetUpdatedTime() uint64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *SuratTafsir) GetWajiz() string {
	if x != nil {
		return x.Wajiz
	}
	return ""
}

var File_pbentity_surat_tafsir_proto protoreflect.FileDescriptor

var file_pbentity_surat_tafsir_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74,
	0x5f, 0x74, 0x61, 0x66, 0x73, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xe3, 0x01, 0x0a, 0x0b, 0x53, 0x75, 0x72, 0x61,
	0x74, 0x54, 0x61, 0x66, 0x73, 0x69, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x61, 0x66, 0x73, 0x69,
	0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x54, 0x61, 0x66, 0x73, 0x69,
	0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x41, 0x79, 0x61, 0x74, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x41, 0x79, 0x61, 0x74, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x54,
	0x61, 0x66, 0x73, 0x69, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61, 0x66,
	0x73, 0x69, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x61, 0x6a, 0x69, 0x7a,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x57, 0x61, 0x6a, 0x69, 0x7a, 0x42, 0x30, 0x5a,
	0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73,
	0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_surat_tafsir_proto_rawDescOnce sync.Once
	file_pbentity_surat_tafsir_proto_rawDescData = file_pbentity_surat_tafsir_proto_rawDesc
)

func file_pbentity_surat_tafsir_proto_rawDescGZIP() []byte {
	file_pbentity_surat_tafsir_proto_rawDescOnce.Do(func() {
		file_pbentity_surat_tafsir_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_surat_tafsir_proto_rawDescData)
	})
	return file_pbentity_surat_tafsir_proto_rawDescData
}

var file_pbentity_surat_tafsir_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surat_tafsir_proto_goTypes = []interface{}{
	(*SuratTafsir)(nil), // 0: pbentity.SuratTafsir
}
var file_pbentity_surat_tafsir_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_surat_tafsir_proto_init() }
func file_pbentity_surat_tafsir_proto_init() {
	if File_pbentity_surat_tafsir_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_surat_tafsir_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuratTafsir); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_surat_tafsir_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surat_tafsir_proto_goTypes,
		DependencyIndexes: file_pbentity_surat_tafsir_proto_depIdxs,
		MessageInfos:      file_pbentity_surat_tafsir_proto_msgTypes,
	}.Build()
	File_pbentity_surat_tafsir_proto = out.File
	file_pbentity_surat_tafsir_proto_rawDesc = nil
	file_pbentity_surat_tafsir_proto_goTypes = nil
	file_pbentity_surat_tafsir_proto_depIdxs = nil
}
