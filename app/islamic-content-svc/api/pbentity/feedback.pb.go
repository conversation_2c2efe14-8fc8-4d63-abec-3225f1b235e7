// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/feedback.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Feedback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                //
	FeedbackType     int32  `protobuf:"varint,2,opt,name=FeedbackType,proto3" json:"FeedbackType,omitempty" dc:"反馈类型 1 系统故障 2 功能建议"`    // 反馈类型 1 系统故障 2 功能建议
	UserId           uint32 `protobuf:"varint,3,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户id"`                              // 用户id
	UserAccount      string `protobuf:"bytes,4,opt,name=UserAccount,proto3" json:"UserAccount,omitempty" dc:"用户账号"`                     // 用户账号
	Images           string `protobuf:"bytes,5,opt,name=Images,proto3" json:"Images,omitempty" dc:"反馈图片 json字符串"`                       // 反馈图片 json字符串
	Desc             string `protobuf:"bytes,6,opt,name=Desc,proto3" json:"Desc,omitempty" dc:"反馈内容"`                                   // 反馈内容
	FeedbackTime     int64  `protobuf:"varint,7,opt,name=FeedbackTime,proto3" json:"FeedbackTime,omitempty" dc:"反馈时间ms"`                // 反馈时间ms
	FeedbackStatus   int32  `protobuf:"varint,8,opt,name=FeedbackStatus,proto3" json:"FeedbackStatus,omitempty" dc:"反馈状态 1 未处理 2已处理"`   // 反馈状态 1 未处理 2已处理
	FeedbackResult   int32  `protobuf:"varint,9,opt,name=FeedbackResult,proto3" json:"FeedbackResult,omitempty" dc:"反馈结果 1 无效反馈 2有效反馈"` // 反馈结果 1 无效反馈 2有效反馈
	CompleteTime     int64  `protobuf:"varint,10,opt,name=CompleteTime,proto3" json:"CompleteTime,omitempty" dc:"处理时间ms"`               // 处理时间ms
	CompleteRemark   string `protobuf:"bytes,11,opt,name=CompleteRemark,proto3" json:"CompleteRemark,omitempty" dc:"处理备注"`              // 处理备注
	CompleteAccount  string `protobuf:"bytes,12,opt,name=CompleteAccount,proto3" json:"CompleteAccount,omitempty" dc:"处理人账号"`           // 处理人账号
	CompleteNickName string `protobuf:"bytes,13,opt,name=CompleteNickName,proto3" json:"CompleteNickName,omitempty" dc:"处理人昵称"`         // 处理人昵称
	CreateTime       int64  `protobuf:"varint,14,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`              // 创建时间(毫秒时间戳)
	UpdateTime       int64  `protobuf:"varint,15,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`              // 更新时间(毫秒时间戳)
	DeleteTime       int64  `protobuf:"varint,16,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`                     // 删除时间
}

func (x *Feedback) Reset() {
	*x = Feedback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_feedback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feedback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feedback) ProtoMessage() {}

func (x *Feedback) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_feedback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feedback.ProtoReflect.Descriptor instead.
func (*Feedback) Descriptor() ([]byte, []int) {
	return file_pbentity_feedback_proto_rawDescGZIP(), []int{0}
}

func (x *Feedback) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Feedback) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *Feedback) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Feedback) GetUserAccount() string {
	if x != nil {
		return x.UserAccount
	}
	return ""
}

func (x *Feedback) GetImages() string {
	if x != nil {
		return x.Images
	}
	return ""
}

func (x *Feedback) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Feedback) GetFeedbackTime() int64 {
	if x != nil {
		return x.FeedbackTime
	}
	return 0
}

func (x *Feedback) GetFeedbackStatus() int32 {
	if x != nil {
		return x.FeedbackStatus
	}
	return 0
}

func (x *Feedback) GetFeedbackResult() int32 {
	if x != nil {
		return x.FeedbackResult
	}
	return 0
}

func (x *Feedback) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *Feedback) GetCompleteRemark() string {
	if x != nil {
		return x.CompleteRemark
	}
	return ""
}

func (x *Feedback) GetCompleteAccount() string {
	if x != nil {
		return x.CompleteAccount
	}
	return ""
}

func (x *Feedback) GetCompleteNickName() string {
	if x != nil {
		return x.CompleteNickName
	}
	return ""
}

func (x *Feedback) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Feedback) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Feedback) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_feedback_proto protoreflect.FileDescriptor

var file_pbentity_feedback_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x22, 0x9a, 0x04, 0x0a, 0x08, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0c, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_feedback_proto_rawDescOnce sync.Once
	file_pbentity_feedback_proto_rawDescData = file_pbentity_feedback_proto_rawDesc
)

func file_pbentity_feedback_proto_rawDescGZIP() []byte {
	file_pbentity_feedback_proto_rawDescOnce.Do(func() {
		file_pbentity_feedback_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_feedback_proto_rawDescData)
	})
	return file_pbentity_feedback_proto_rawDescData
}

var file_pbentity_feedback_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_feedback_proto_goTypes = []interface{}{
	(*Feedback)(nil), // 0: pbentity.Feedback
}
var file_pbentity_feedback_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_feedback_proto_init() }
func file_pbentity_feedback_proto_init() {
	if File_pbentity_feedback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_feedback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feedback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_feedback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_feedback_proto_goTypes,
		DependencyIndexes: file_pbentity_feedback_proto_depIdxs,
		MessageInfos:      file_pbentity_feedback_proto_msgTypes,
	}.Build()
	File_pbentity_feedback_proto = out.File
	file_pbentity_feedback_proto_rawDesc = nil
	file_pbentity_feedback_proto_goTypes = nil
	file_pbentity_feedback_proto_depIdxs = nil
}
