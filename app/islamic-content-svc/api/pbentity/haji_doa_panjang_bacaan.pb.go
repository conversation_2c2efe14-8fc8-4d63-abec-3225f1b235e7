// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/haji_doa_panjang_bacaan.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiDoaPanjangBacaan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                              // 主键ID
	DoaId      uint64 `protobuf:"varint,2,opt,name=DoaId,proto3" json:"DoaId,omitempty" dc:"祈祷文ID，关联haji_doa_panjang.id"` // 祈祷文ID，关联haji_doa_panjang.id
	BacaanNo   int32  `protobuf:"varint,3,opt,name=BacaanNo,proto3" json:"BacaanNo,omitempty" dc:"诵读序号"`                  // 诵读序号
	BacaanName string `protobuf:"bytes,4,opt,name=BacaanName,proto3" json:"BacaanName,omitempty" dc:"诵读名称"`               // 诵读名称
	CreateTime uint64 `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`       // 创建时间（毫秒时间戳）
	UpdateTime uint64 `protobuf:"varint,6,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`       // 更新时间（毫秒时间戳）
}

func (x *HajiDoaPanjangBacaan) Reset() {
	*x = HajiDoaPanjangBacaan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_haji_doa_panjang_bacaan_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HajiDoaPanjangBacaan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaan) ProtoMessage() {}

func (x *HajiDoaPanjangBacaan) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_doa_panjang_bacaan_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaan.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaan) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_doa_panjang_bacaan_proto_rawDescGZIP(), []int{0}
}

func (x *HajiDoaPanjangBacaan) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaPanjangBacaan) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *HajiDoaPanjangBacaan) GetBacaanNo() int32 {
	if x != nil {
		return x.BacaanNo
	}
	return 0
}

func (x *HajiDoaPanjangBacaan) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *HajiDoaPanjangBacaan) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiDoaPanjangBacaan) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_doa_panjang_bacaan_proto protoreflect.FileDescriptor

var file_pbentity_haji_doa_panjang_bacaan_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x61, 0x6a, 0x69, 0x5f,
	0x64, 0x6f, 0x61, 0x5f, 0x70, 0x61, 0x6e, 0x6a, 0x61, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x22, 0xb8, 0x01, 0x0a, 0x14, 0x48, 0x61, 0x6a, 0x69, 0x44, 0x6f, 0x61, 0x50, 0x61,
	0x6e, 0x6a, 0x61, 0x6e, 0x67, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x44,
	0x6f, 0x61, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x44, 0x6f, 0x61, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4e, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4e, 0x6f, 0x12, 0x1e, 0x0a,
	0x0a, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a,
	0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73,
	0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_haji_doa_panjang_bacaan_proto_rawDescOnce sync.Once
	file_pbentity_haji_doa_panjang_bacaan_proto_rawDescData = file_pbentity_haji_doa_panjang_bacaan_proto_rawDesc
)

func file_pbentity_haji_doa_panjang_bacaan_proto_rawDescGZIP() []byte {
	file_pbentity_haji_doa_panjang_bacaan_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_doa_panjang_bacaan_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_haji_doa_panjang_bacaan_proto_rawDescData)
	})
	return file_pbentity_haji_doa_panjang_bacaan_proto_rawDescData
}

var file_pbentity_haji_doa_panjang_bacaan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_doa_panjang_bacaan_proto_goTypes = []interface{}{
	(*HajiDoaPanjangBacaan)(nil), // 0: pbentity.HajiDoaPanjangBacaan
}
var file_pbentity_haji_doa_panjang_bacaan_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_doa_panjang_bacaan_proto_init() }
func file_pbentity_haji_doa_panjang_bacaan_proto_init() {
	if File_pbentity_haji_doa_panjang_bacaan_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_haji_doa_panjang_bacaan_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HajiDoaPanjangBacaan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_haji_doa_panjang_bacaan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_doa_panjang_bacaan_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_doa_panjang_bacaan_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_doa_panjang_bacaan_proto_msgTypes,
	}.Build()
	File_pbentity_haji_doa_panjang_bacaan_proto = out.File
	file_pbentity_haji_doa_panjang_bacaan_proto_rawDesc = nil
	file_pbentity_haji_doa_panjang_bacaan_proto_goTypes = nil
	file_pbentity_haji_doa_panjang_bacaan_proto_depIdxs = nil
}
