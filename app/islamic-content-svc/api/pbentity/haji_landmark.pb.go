// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/haji_landmark.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiLandmark struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                   // 主键ID
	TypeId     uint64 `protobuf:"varint,2,opt,name=TypeId,proto3" json:"TypeId,omitempty" dc:"地标类型ID，关联haji_landmark_type.id"` // 地标类型ID，关联haji_landmark_type.id
	InnerType  string `protobuf:"bytes,3,opt,name=InnerType,proto3" json:"InnerType,omitempty" dc:"内部类型: (destinasi, tokoh)"`  // 内部类型: (destinasi, tokoh)
	Latitude   string `protobuf:"bytes,4,opt,name=Latitude,proto3" json:"Latitude,omitempty" dc:"纬度"`                          // 纬度
	Longitude  string `protobuf:"bytes,5,opt,name=Longitude,proto3" json:"Longitude,omitempty" dc:"经度"`                        // 经度
	ImageUrl   string `protobuf:"bytes,6,opt,name=ImageUrl,proto3" json:"ImageUrl,omitempty" dc:"图片URL"`                       // 图片URL
	SortOrder  uint32 `protobuf:"varint,7,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序值，数字越小排序越靠前"`            // 排序值，数字越小排序越靠前
	CreateTime uint64 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`            // 创建时间（毫秒时间戳）
	UpdateTime uint64 `protobuf:"varint,9,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`            // 更新时间（毫秒时间戳）
}

func (x *HajiLandmark) Reset() {
	*x = HajiLandmark{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_haji_landmark_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HajiLandmark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmark) ProtoMessage() {}

func (x *HajiLandmark) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_landmark_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmark.ProtoReflect.Descriptor instead.
func (*HajiLandmark) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_landmark_proto_rawDescGZIP(), []int{0}
}

func (x *HajiLandmark) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiLandmark) GetTypeId() uint64 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *HajiLandmark) GetInnerType() string {
	if x != nil {
		return x.InnerType
	}
	return ""
}

func (x *HajiLandmark) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *HajiLandmark) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *HajiLandmark) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *HajiLandmark) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *HajiLandmark) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiLandmark) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_landmark_proto protoreflect.FileDescriptor

var file_pbentity_haji_landmark_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x61, 0x6a, 0x69, 0x5f,
	0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08,
	0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x88, 0x02, 0x0a, 0x0c, 0x48, 0x61, 0x6a,
	0x69, 0x4c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x4c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_haji_landmark_proto_rawDescOnce sync.Once
	file_pbentity_haji_landmark_proto_rawDescData = file_pbentity_haji_landmark_proto_rawDesc
)

func file_pbentity_haji_landmark_proto_rawDescGZIP() []byte {
	file_pbentity_haji_landmark_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_landmark_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_haji_landmark_proto_rawDescData)
	})
	return file_pbentity_haji_landmark_proto_rawDescData
}

var file_pbentity_haji_landmark_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_landmark_proto_goTypes = []interface{}{
	(*HajiLandmark)(nil), // 0: pbentity.HajiLandmark
}
var file_pbentity_haji_landmark_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_landmark_proto_init() }
func file_pbentity_haji_landmark_proto_init() {
	if File_pbentity_haji_landmark_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_haji_landmark_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HajiLandmark); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_haji_landmark_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_landmark_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_landmark_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_landmark_proto_msgTypes,
	}.Build()
	File_pbentity_haji_landmark_proto = out.File
	file_pbentity_haji_landmark_proto_rawDesc = nil
	file_pbentity_haji_landmark_proto_goTypes = nil
	file_pbentity_haji_landmark_proto_depIdxs = nil
}
