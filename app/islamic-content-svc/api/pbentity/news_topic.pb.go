// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/news_topic.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsTopic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                     //
	Counts     uint32 `protobuf:"varint,2,opt,name=Counts,proto3" json:"Counts,omitempty" dc:"文章数量"`                   // 文章数量
	IsZh       uint32 `protobuf:"varint,3,opt,name=IsZh,proto3" json:"IsZh,omitempty" dc:"是否中文，0-否，1-是"`               // 是否中文，0-否，1-是
	IsEn       uint32 `protobuf:"varint,4,opt,name=IsEn,proto3" json:"IsEn,omitempty" dc:"是否英文，0-否，1-是"`               // 是否英文，0-否，1-是
	IsId       uint32 `protobuf:"varint,5,opt,name=IsId,proto3" json:"IsId,omitempty" dc:"是否印尼文，0-否，1-是"`              // 是否印尼文，0-否，1-是
	Status     uint32 `protobuf:"varint,6,opt,name=Status,proto3" json:"Status,omitempty" dc:"是否显示，1启用，0关闭"`           // 是否显示，1启用，0关闭
	Sort       uint32 `protobuf:"varint,7,opt,name=Sort,proto3" json:"Sort,omitempty" dc:"排序，数字越小，排序越靠前"`              // 排序，数字越小，排序越靠前
	AdminId    uint32 `protobuf:"varint,8,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"分类负责人id"`              // 分类负责人id
	TopicImgs  string `protobuf:"bytes,9,opt,name=TopicImgs,proto3" json:"TopicImgs,omitempty" dc:"专题图片"`              // 专题图片
	IsAppShow  int32  `protobuf:"varint,10,opt,name=IsAppShow,proto3" json:"IsAppShow,omitempty" dc:"是否app展示，1：是 0：否"` // 是否app展示，1：是 0：否
	Creater    uint32 `protobuf:"varint,11,opt,name=Creater,proto3" json:"Creater,omitempty" dc:"创建者id"`               // 创建者id
	CreateName string `protobuf:"bytes,12,opt,name=CreateName,proto3" json:"CreateName,omitempty" dc:"创建者"`            // 创建者
	CreateTime int64  `protobuf:"varint,13,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`          // 创建时间
	UpdateTime int64  `protobuf:"varint,14,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`          // 修改时间
	DeleteTime int64  `protobuf:"varint,15,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`          // 删除时间
}

func (x *NewsTopic) Reset() {
	*x = NewsTopic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_news_topic_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopic) ProtoMessage() {}

func (x *NewsTopic) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_topic_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopic.ProtoReflect.Descriptor instead.
func (*NewsTopic) Descriptor() ([]byte, []int) {
	return file_pbentity_news_topic_proto_rawDescGZIP(), []int{0}
}

func (x *NewsTopic) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsTopic) GetCounts() uint32 {
	if x != nil {
		return x.Counts
	}
	return 0
}

func (x *NewsTopic) GetIsZh() uint32 {
	if x != nil {
		return x.IsZh
	}
	return 0
}

func (x *NewsTopic) GetIsEn() uint32 {
	if x != nil {
		return x.IsEn
	}
	return 0
}

func (x *NewsTopic) GetIsId() uint32 {
	if x != nil {
		return x.IsId
	}
	return 0
}

func (x *NewsTopic) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *NewsTopic) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *NewsTopic) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *NewsTopic) GetTopicImgs() string {
	if x != nil {
		return x.TopicImgs
	}
	return ""
}

func (x *NewsTopic) GetIsAppShow() int32 {
	if x != nil {
		return x.IsAppShow
	}
	return 0
}

func (x *NewsTopic) GetCreater() uint32 {
	if x != nil {
		return x.Creater
	}
	return 0
}

func (x *NewsTopic) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *NewsTopic) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsTopic) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsTopic) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_topic_proto protoreflect.FileDescriptor

var file_pbentity_news_topic_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x8b, 0x03, 0x0a, 0x09, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x49,
	0x73, 0x5a, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x49, 0x73, 0x5a, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x49, 0x73, 0x45, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x49,
	0x73, 0x45, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x73, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x49, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x53, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x53,
	0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6d, 0x67, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6d, 0x67, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x49,
	0x73, 0x41, 0x70, 0x70, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x49, 0x73, 0x41, 0x70, 0x70, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_news_topic_proto_rawDescOnce sync.Once
	file_pbentity_news_topic_proto_rawDescData = file_pbentity_news_topic_proto_rawDesc
)

func file_pbentity_news_topic_proto_rawDescGZIP() []byte {
	file_pbentity_news_topic_proto_rawDescOnce.Do(func() {
		file_pbentity_news_topic_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_news_topic_proto_rawDescData)
	})
	return file_pbentity_news_topic_proto_rawDescData
}

var file_pbentity_news_topic_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_topic_proto_goTypes = []interface{}{
	(*NewsTopic)(nil), // 0: pbentity.NewsTopic
}
var file_pbentity_news_topic_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_topic_proto_init() }
func file_pbentity_news_topic_proto_init() {
	if File_pbentity_news_topic_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_news_topic_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTopic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_news_topic_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_topic_proto_goTypes,
		DependencyIndexes: file_pbentity_news_topic_proto_depIdxs,
		MessageInfos:      file_pbentity_news_topic_proto_msgTypes,
	}.Build()
	File_pbentity_news_topic_proto = out.File
	file_pbentity_news_topic_proto_rawDesc = nil
	file_pbentity_news_topic_proto_goTypes = nil
	file_pbentity_news_topic_proto_depIdxs = nil
}
