// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/doa_categories.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DoaCategories struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                   //
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                //
	Slug          string                 `protobuf:"bytes,3,opt,name=Slug,proto3" json:"Slug,omitempty"`                //
	OrderNumber   int32                  `protobuf:"varint,4,opt,name=OrderNumber,proto3" json:"OrderNumber,omitempty"` //
	ViewCount     int32                  `protobuf:"varint,5,opt,name=ViewCount,proto3" json:"ViewCount,omitempty"`     //
	GroupName     string                 `protobuf:"bytes,6,opt,name=GroupName,proto3" json:"GroupName,omitempty"`      //
	Total         int32                  `protobuf:"varint,7,opt,name=Total,proto3" json:"Total,omitempty"`             //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaCategories) Reset() {
	*x = DoaCategories{}
	mi := &file_pbentity_doa_categories_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaCategories) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaCategories) ProtoMessage() {}

func (x *DoaCategories) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_doa_categories_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaCategories.ProtoReflect.Descriptor instead.
func (*DoaCategories) Descriptor() ([]byte, []int) {
	return file_pbentity_doa_categories_proto_rawDescGZIP(), []int{0}
}

func (x *DoaCategories) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaCategories) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaCategories) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

func (x *DoaCategories) GetOrderNumber() int32 {
	if x != nil {
		return x.OrderNumber
	}
	return 0
}

func (x *DoaCategories) GetViewCount() int32 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *DoaCategories) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *DoaCategories) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_pbentity_doa_categories_proto protoreflect.FileDescriptor

const file_pbentity_doa_categories_proto_rawDesc = "" +
	"\n" +
	"\x1dpbentity/doa_categories.proto\x12\bpbentity\"\xbb\x01\n" +
	"\rDoaCategories\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x12\n" +
	"\x04Slug\x18\x03 \x01(\tR\x04Slug\x12 \n" +
	"\vOrderNumber\x18\x04 \x01(\x05R\vOrderNumber\x12\x1c\n" +
	"\tViewCount\x18\x05 \x01(\x05R\tViewCount\x12\x1c\n" +
	"\tGroupName\x18\x06 \x01(\tR\tGroupName\x12\x14\n" +
	"\x05Total\x18\a \x01(\x05R\x05TotalB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_doa_categories_proto_rawDescOnce sync.Once
	file_pbentity_doa_categories_proto_rawDescData []byte
)

func file_pbentity_doa_categories_proto_rawDescGZIP() []byte {
	file_pbentity_doa_categories_proto_rawDescOnce.Do(func() {
		file_pbentity_doa_categories_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_doa_categories_proto_rawDesc), len(file_pbentity_doa_categories_proto_rawDesc)))
	})
	return file_pbentity_doa_categories_proto_rawDescData
}

var file_pbentity_doa_categories_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_doa_categories_proto_goTypes = []any{
	(*DoaCategories)(nil), // 0: pbentity.DoaCategories
}
var file_pbentity_doa_categories_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_doa_categories_proto_init() }
func file_pbentity_doa_categories_proto_init() {
	if File_pbentity_doa_categories_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_doa_categories_proto_rawDesc), len(file_pbentity_doa_categories_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_doa_categories_proto_goTypes,
		DependencyIndexes: file_pbentity_doa_categories_proto_depIdxs,
		MessageInfos:      file_pbentity_doa_categories_proto_msgTypes,
	}.Build()
	File_pbentity_doa_categories_proto = out.File
	file_pbentity_doa_categories_proto_goTypes = nil
	file_pbentity_doa_categories_proto_depIdxs = nil
}
