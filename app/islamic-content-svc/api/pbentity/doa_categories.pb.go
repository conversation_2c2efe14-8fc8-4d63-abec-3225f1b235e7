// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/doa_categories.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DoaCategories struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                   //
	Name        string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                //
	Slug        string `protobuf:"bytes,3,opt,name=Slug,proto3" json:"Slug,omitempty"`                //
	OrderNumber int32  `protobuf:"varint,4,opt,name=OrderNumber,proto3" json:"OrderNumber,omitempty"` //
	ViewCount   int32  `protobuf:"varint,5,opt,name=ViewCount,proto3" json:"ViewCount,omitempty"`     //
	GroupName   string `protobuf:"bytes,6,opt,name=GroupName,proto3" json:"GroupName,omitempty"`      //
	Total       int32  `protobuf:"varint,7,opt,name=Total,proto3" json:"Total,omitempty"`             //
}

func (x *DoaCategories) Reset() {
	*x = DoaCategories{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_doa_categories_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaCategories) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaCategories) ProtoMessage() {}

func (x *DoaCategories) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_doa_categories_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaCategories.ProtoReflect.Descriptor instead.
func (*DoaCategories) Descriptor() ([]byte, []int) {
	return file_pbentity_doa_categories_proto_rawDescGZIP(), []int{0}
}

func (x *DoaCategories) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaCategories) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaCategories) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

func (x *DoaCategories) GetOrderNumber() int32 {
	if x != nil {
		return x.OrderNumber
	}
	return 0
}

func (x *DoaCategories) GetViewCount() int32 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *DoaCategories) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *DoaCategories) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_pbentity_doa_categories_proto protoreflect.FileDescriptor

var file_pbentity_doa_categories_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x64, 0x6f, 0x61, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xbb, 0x01, 0x0a, 0x0d, 0x44, 0x6f,
	0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x53, 0x6c, 0x75, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53,
	0x6c, 0x75, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c,
	0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbentity_doa_categories_proto_rawDescOnce sync.Once
	file_pbentity_doa_categories_proto_rawDescData = file_pbentity_doa_categories_proto_rawDesc
)

func file_pbentity_doa_categories_proto_rawDescGZIP() []byte {
	file_pbentity_doa_categories_proto_rawDescOnce.Do(func() {
		file_pbentity_doa_categories_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_doa_categories_proto_rawDescData)
	})
	return file_pbentity_doa_categories_proto_rawDescData
}

var file_pbentity_doa_categories_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_doa_categories_proto_goTypes = []interface{}{
	(*DoaCategories)(nil), // 0: pbentity.DoaCategories
}
var file_pbentity_doa_categories_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_doa_categories_proto_init() }
func file_pbentity_doa_categories_proto_init() {
	if File_pbentity_doa_categories_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_doa_categories_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaCategories); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_doa_categories_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_doa_categories_proto_goTypes,
		DependencyIndexes: file_pbentity_doa_categories_proto_depIdxs,
		MessageInfos:      file_pbentity_doa_categories_proto_msgTypes,
	}.Build()
	File_pbentity_doa_categories_proto = out.File
	file_pbentity_doa_categories_proto_rawDesc = nil
	file_pbentity_doa_categories_proto_goTypes = nil
	file_pbentity_doa_categories_proto_depIdxs = nil
}
