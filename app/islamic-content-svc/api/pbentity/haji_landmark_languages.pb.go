// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/haji_landmark_languages.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiLandmarkLanguages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                   // 主键ID
	LandmarkId       uint64 `protobuf:"varint,2,opt,name=LandmarkId,proto3" json:"LandmarkId,omitempty" dc:"地标ID，关联haji_landmark.id"`                // 地标ID，关联haji_landmark.id
	LanguageId       uint32 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID：0-中文，1-英文，2-印尼语"`                   // 语言ID：0-中文，1-英文，2-印尼语
	LandmarkName     string `protobuf:"bytes,4,opt,name=LandmarkName,proto3" json:"LandmarkName,omitempty" dc:"地标名称"`                                // 地标名称
	Country          string `protobuf:"bytes,5,opt,name=Country,proto3" json:"Country,omitempty" dc:"国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)"` // 国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)
	Address          string `protobuf:"bytes,6,opt,name=Address,proto3" json:"Address,omitempty" dc:"详细地址"`                                          // 详细地址
	ShortDescription string `protobuf:"bytes,7,opt,name=ShortDescription,proto3" json:"ShortDescription,omitempty" dc:"简介"`                          // 简介
	InformationText  string `protobuf:"bytes,8,opt,name=InformationText,proto3" json:"InformationText,omitempty" dc:"详细介绍"`                          // 详细介绍
	CreateTime       uint64 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`                            // 创建时间（毫秒时间戳）
	UpdateTime       uint64 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`                           // 更新时间（毫秒时间戳）
}

func (x *HajiLandmarkLanguages) Reset() {
	*x = HajiLandmarkLanguages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_haji_landmark_languages_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HajiLandmarkLanguages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkLanguages) ProtoMessage() {}

func (x *HajiLandmarkLanguages) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_landmark_languages_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkLanguages.ProtoReflect.Descriptor instead.
func (*HajiLandmarkLanguages) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_landmark_languages_proto_rawDescGZIP(), []int{0}
}

func (x *HajiLandmarkLanguages) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiLandmarkLanguages) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *HajiLandmarkLanguages) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *HajiLandmarkLanguages) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

func (x *HajiLandmarkLanguages) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *HajiLandmarkLanguages) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *HajiLandmarkLanguages) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *HajiLandmarkLanguages) GetInformationText() string {
	if x != nil {
		return x.InformationText
	}
	return ""
}

func (x *HajiLandmarkLanguages) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiLandmarkLanguages) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_landmark_languages_proto protoreflect.FileDescriptor

var file_pbentity_haji_landmark_languages_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x61, 0x6a, 0x69, 0x5f,
	0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x22, 0xd5, 0x02, 0x0a, 0x15, 0x48, 0x61, 0x6a, 0x69, 0x4c, 0x61, 0x6e, 0x64, 0x6d,
	0x61, 0x72, 0x6b, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x4c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x4c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x4c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x53, 0x68, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x28, 0x0a, 0x0f, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61,
	0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_haji_landmark_languages_proto_rawDescOnce sync.Once
	file_pbentity_haji_landmark_languages_proto_rawDescData = file_pbentity_haji_landmark_languages_proto_rawDesc
)

func file_pbentity_haji_landmark_languages_proto_rawDescGZIP() []byte {
	file_pbentity_haji_landmark_languages_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_landmark_languages_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_haji_landmark_languages_proto_rawDescData)
	})
	return file_pbentity_haji_landmark_languages_proto_rawDescData
}

var file_pbentity_haji_landmark_languages_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_landmark_languages_proto_goTypes = []interface{}{
	(*HajiLandmarkLanguages)(nil), // 0: pbentity.HajiLandmarkLanguages
}
var file_pbentity_haji_landmark_languages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_landmark_languages_proto_init() }
func file_pbentity_haji_landmark_languages_proto_init() {
	if File_pbentity_haji_landmark_languages_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_haji_landmark_languages_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HajiLandmarkLanguages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_haji_landmark_languages_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_landmark_languages_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_landmark_languages_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_landmark_languages_proto_msgTypes,
	}.Build()
	File_pbentity_haji_landmark_languages_proto = out.File
	file_pbentity_haji_landmark_languages_proto_rawDesc = nil
	file_pbentity_haji_landmark_languages_proto_goTypes = nil
	file_pbentity_haji_landmark_languages_proto_depIdxs = nil
}
