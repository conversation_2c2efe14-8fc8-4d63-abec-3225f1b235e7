// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/advertisement_languages.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdvertisementLanguages struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                           // 主键ID
	AdvertisementId  uint32                 `protobuf:"varint,2,opt,name=AdvertisementId,proto3" json:"AdvertisementId,omitempty" dc:"广告ID"`                 // 广告ID
	LanguageId       uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`        // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title            string                 `protobuf:"bytes,4,opt,name=Title,proto3" json:"Title,omitempty" dc:"广告名称"`                                      // 广告名称
	Description      string                 `protobuf:"bytes,5,opt,name=Description,proto3" json:"Description,omitempty" dc:"广告描述"`                          // 广告描述
	DisplayType      uint32                 `protobuf:"varint,6,opt,name=DisplayType,proto3" json:"DisplayType,omitempty" dc:"显示类型: 1-单图固定, 2-多图轮播"`         // 显示类型: 1-单图固定, 2-多图轮播
	CarouselInterval uint32                 `protobuf:"varint,7,opt,name=CarouselInterval,proto3" json:"CarouselInterval,omitempty" dc:"轮播间隔时间(秒)，仅多图轮播时有效"` // 轮播间隔时间(秒)，仅多图轮播时有效
	AdminId          uint32                 `protobuf:"varint,8,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"创建管理员ID"`                              // 创建管理员ID
	CreateTime       uint64                 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`                    // 创建时间(毫秒时间戳)
	UpdateTime       uint64                 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`                   // 更新时间(毫秒时间戳)
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AdvertisementLanguages) Reset() {
	*x = AdvertisementLanguages{}
	mi := &file_pbentity_advertisement_languages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvertisementLanguages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvertisementLanguages) ProtoMessage() {}

func (x *AdvertisementLanguages) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_advertisement_languages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvertisementLanguages.ProtoReflect.Descriptor instead.
func (*AdvertisementLanguages) Descriptor() ([]byte, []int) {
	return file_pbentity_advertisement_languages_proto_rawDescGZIP(), []int{0}
}

func (x *AdvertisementLanguages) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AdvertisementLanguages) GetAdvertisementId() uint32 {
	if x != nil {
		return x.AdvertisementId
	}
	return 0
}

func (x *AdvertisementLanguages) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *AdvertisementLanguages) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AdvertisementLanguages) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AdvertisementLanguages) GetDisplayType() uint32 {
	if x != nil {
		return x.DisplayType
	}
	return 0
}

func (x *AdvertisementLanguages) GetCarouselInterval() uint32 {
	if x != nil {
		return x.CarouselInterval
	}
	return 0
}

func (x *AdvertisementLanguages) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *AdvertisementLanguages) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *AdvertisementLanguages) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_advertisement_languages_proto protoreflect.FileDescriptor

const file_pbentity_advertisement_languages_proto_rawDesc = "" +
	"\n" +
	"&pbentity/advertisement_languages.proto\x12\bpbentity\"\xd2\x02\n" +
	"\x16AdvertisementLanguages\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12(\n" +
	"\x0fAdvertisementId\x18\x02 \x01(\rR\x0fAdvertisementId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x14\n" +
	"\x05Title\x18\x04 \x01(\tR\x05Title\x12 \n" +
	"\vDescription\x18\x05 \x01(\tR\vDescription\x12 \n" +
	"\vDisplayType\x18\x06 \x01(\rR\vDisplayType\x12*\n" +
	"\x10CarouselInterval\x18\a \x01(\rR\x10CarouselInterval\x12\x18\n" +
	"\aAdminId\x18\b \x01(\rR\aAdminId\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\t \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\n" +
	" \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_advertisement_languages_proto_rawDescOnce sync.Once
	file_pbentity_advertisement_languages_proto_rawDescData []byte
)

func file_pbentity_advertisement_languages_proto_rawDescGZIP() []byte {
	file_pbentity_advertisement_languages_proto_rawDescOnce.Do(func() {
		file_pbentity_advertisement_languages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_languages_proto_rawDesc), len(file_pbentity_advertisement_languages_proto_rawDesc)))
	})
	return file_pbentity_advertisement_languages_proto_rawDescData
}

var file_pbentity_advertisement_languages_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_advertisement_languages_proto_goTypes = []any{
	(*AdvertisementLanguages)(nil), // 0: pbentity.AdvertisementLanguages
}
var file_pbentity_advertisement_languages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_advertisement_languages_proto_init() }
func file_pbentity_advertisement_languages_proto_init() {
	if File_pbentity_advertisement_languages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_advertisement_languages_proto_rawDesc), len(file_pbentity_advertisement_languages_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_advertisement_languages_proto_goTypes,
		DependencyIndexes: file_pbentity_advertisement_languages_proto_depIdxs,
		MessageInfos:      file_pbentity_advertisement_languages_proto_msgTypes,
	}.Build()
	File_pbentity_advertisement_languages_proto = out.File
	file_pbentity_advertisement_languages_proto_goTypes = nil
	file_pbentity_advertisement_languages_proto_depIdxs = nil
}
