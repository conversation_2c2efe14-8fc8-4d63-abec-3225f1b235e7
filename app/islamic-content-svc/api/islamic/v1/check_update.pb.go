// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/check_update.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform       string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" dc:"平台"`                                     // 平台
	CurrentVersion string `protobuf:"bytes,2,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty" dc:"当前版本"` // 当前版本
}

func (x *CheckUpdateReq) Reset() {
	*x = CheckUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_check_update_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateReq) ProtoMessage() {}

func (x *CheckUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateReq.ProtoReflect.Descriptor instead.
func (*CheckUpdateReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{0}
}

func (x *CheckUpdateReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CheckUpdateReq) GetCurrentVersion() string {
	if x != nil {
		return x.CurrentVersion
	}
	return ""
}

type CheckUpdateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *CheckUpdateData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckUpdateRes) Reset() {
	*x = CheckUpdateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_check_update_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateRes) ProtoMessage() {}

func (x *CheckUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateRes.ProtoReflect.Descriptor instead.
func (*CheckUpdateRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{1}
}

func (x *CheckUpdateRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUpdateRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckUpdateRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckUpdateRes) GetData() *CheckUpdateData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CheckUpdateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUpdate      *bool   `protobuf:"varint,1,opt,name=is_update,json=isUpdate,proto3,oneof" json:"is_update,omitempty" dc:"是否需要更新"`             // 是否需要更新
	ForceUpdate   *bool   `protobuf:"varint,2,opt,name=force_update,json=forceUpdate,proto3,oneof" json:"force_update,omitempty" dc:"是否强制更新"`    // 是否强制更新
	LatestVersion *string `protobuf:"bytes,3,opt,name=latest_version,json=latestVersion,proto3,oneof" json:"latest_version,omitempty" dc:"最新版本"` // 最新版本
	UpdateUrl     *string `protobuf:"bytes,4,opt,name=update_url,json=updateUrl,proto3,oneof" json:"update_url,omitempty" dc:"下载地址"`             // 下载地址
	FileSize      *int32  `protobuf:"varint,5,opt,name=file_size,json=fileSize,proto3,oneof" json:"file_size,omitempty" dc:"文件大小"`               // 文件大小
	ReleaseDate   *string `protobuf:"bytes,6,opt,name=release_date,json=releaseDate,proto3,oneof" json:"release_date,omitempty" dc:"发布时间"`       // 发布时间
	Md5           *string `protobuf:"bytes,7,opt,name=md5,proto3,oneof" json:"md5,omitempty" dc:"文件md5"`                                         // 文件md5
}

func (x *CheckUpdateData) Reset() {
	*x = CheckUpdateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_check_update_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUpdateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateData) ProtoMessage() {}

func (x *CheckUpdateData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateData.ProtoReflect.Descriptor instead.
func (*CheckUpdateData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{2}
}

func (x *CheckUpdateData) GetIsUpdate() bool {
	if x != nil && x.IsUpdate != nil {
		return *x.IsUpdate
	}
	return false
}

func (x *CheckUpdateData) GetForceUpdate() bool {
	if x != nil && x.ForceUpdate != nil {
		return *x.ForceUpdate
	}
	return false
}

func (x *CheckUpdateData) GetLatestVersion() string {
	if x != nil && x.LatestVersion != nil {
		return *x.LatestVersion
	}
	return ""
}

func (x *CheckUpdateData) GetUpdateUrl() string {
	if x != nil && x.UpdateUrl != nil {
		return *x.UpdateUrl
	}
	return ""
}

func (x *CheckUpdateData) GetFileSize() int32 {
	if x != nil && x.FileSize != nil {
		return *x.FileSize
	}
	return 0
}

func (x *CheckUpdateData) GetReleaseDate() string {
	if x != nil && x.ReleaseDate != nil {
		return *x.ReleaseDate
	}
	return ""
}

func (x *CheckUpdateData) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

var File_islamic_v1_check_update_proto protoreflect.FileDescriptor

var file_islamic_v1_check_update_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x55,
	0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0xf4, 0x02, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69,
	0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x01, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0d, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0b, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x03, 0x6d, 0x64, 0x35,
	0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6d, 0x64, 0x35, 0x32, 0x5b, 0x0a, 0x12, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x45, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61,
	0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_check_update_proto_rawDescOnce sync.Once
	file_islamic_v1_check_update_proto_rawDescData = file_islamic_v1_check_update_proto_rawDesc
)

func file_islamic_v1_check_update_proto_rawDescGZIP() []byte {
	file_islamic_v1_check_update_proto_rawDescOnce.Do(func() {
		file_islamic_v1_check_update_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_check_update_proto_rawDescData)
	})
	return file_islamic_v1_check_update_proto_rawDescData
}

var file_islamic_v1_check_update_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_islamic_v1_check_update_proto_goTypes = []interface{}{
	(*CheckUpdateReq)(nil),  // 0: islamic.v1.CheckUpdateReq
	(*CheckUpdateRes)(nil),  // 1: islamic.v1.CheckUpdateRes
	(*CheckUpdateData)(nil), // 2: islamic.v1.CheckUpdateData
	(*common.Error)(nil),    // 3: common.Error
}
var file_islamic_v1_check_update_proto_depIdxs = []int32{
	3, // 0: islamic.v1.CheckUpdateRes.error:type_name -> common.Error
	2, // 1: islamic.v1.CheckUpdateRes.data:type_name -> islamic.v1.CheckUpdateData
	0, // 2: islamic.v1.CheckUpdateService.CheckUpdate:input_type -> islamic.v1.CheckUpdateReq
	1, // 3: islamic.v1.CheckUpdateService.CheckUpdate:output_type -> islamic.v1.CheckUpdateRes
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_islamic_v1_check_update_proto_init() }
func file_islamic_v1_check_update_proto_init() {
	if File_islamic_v1_check_update_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_check_update_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_check_update_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUpdateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_check_update_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUpdateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_islamic_v1_check_update_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_check_update_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_check_update_proto_goTypes,
		DependencyIndexes: file_islamic_v1_check_update_proto_depIdxs,
		MessageInfos:      file_islamic_v1_check_update_proto_msgTypes,
	}.Build()
	File_islamic_v1_check_update_proto = out.File
	file_islamic_v1_check_update_proto_rawDesc = nil
	file_islamic_v1_check_update_proto_goTypes = nil
	file_islamic_v1_check_update_proto_depIdxs = nil
}
