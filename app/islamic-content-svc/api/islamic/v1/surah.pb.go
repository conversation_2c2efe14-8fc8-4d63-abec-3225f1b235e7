// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`                                // 章节id
	Name      string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                               //名称
	IsPopular uint32              `protobuf:"varint,3,opt,name=is_popular,json=isPopular,proto3" json:"is_popular,omitempty" dc:"是否热门"` // 是否热门
	Page      *common.PageRequest `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                             // 分页参数
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurahListReq) GetIsPopular() uint32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

func (x *SurahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SuratDaftarInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Nomor       uint32 `protobuf:"varint,2,opt,name=Nomor,proto3" json:"Nomor,omitempty"`
	Nama        string `protobuf:"bytes,3,opt,name=Nama,proto3" json:"Nama,omitempty"`
	NamaLatin   string `protobuf:"bytes,4,opt,name=NamaLatin,proto3" json:"NamaLatin,omitempty"`
	JumlahAyat  uint32 `protobuf:"varint,5,opt,name=JumlahAyat,proto3" json:"JumlahAyat,omitempty"`
	TempatTurun string `protobuf:"bytes,6,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty"`
	Arti        string `protobuf:"bytes,7,opt,name=Arti,proto3" json:"Arti,omitempty"`
	Deskripsi   string `protobuf:"bytes,8,opt,name=Deskripsi,proto3" json:"Deskripsi,omitempty"`
	Audio       string `protobuf:"bytes,9,opt,name=Audio,proto3" json:"Audio,omitempty"`
}

func (x *SuratDaftarInfo) Reset() {
	*x = SuratDaftarInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuratDaftarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratDaftarInfo) ProtoMessage() {}

func (x *SuratDaftarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratDaftarInfo.ProtoReflect.Descriptor instead.
func (*SuratDaftarInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SuratDaftarInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratDaftarInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratDaftarInfo) GetNama() string {
	if x != nil {
		return x.Nama
	}
	return ""
}

func (x *SuratDaftarInfo) GetNamaLatin() string {
	if x != nil {
		return x.NamaLatin
	}
	return ""
}

func (x *SuratDaftarInfo) GetJumlahAyat() uint32 {
	if x != nil {
		return x.JumlahAyat
	}
	return 0
}

func (x *SuratDaftarInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

func (x *SuratDaftarInfo) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *SuratDaftarInfo) GetDeskripsi() string {
	if x != nil {
		return x.Deskripsi
	}
	return ""
}

func (x *SuratDaftarInfo) GetAudio() string {
	if x != nil {
		return x.Audio
	}
	return ""
}

type SurahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SuratDaftarInfo   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *SurahListResData) Reset() {
	*x = SurahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListResData) ProtoMessage() {}

func (x *SurahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListResData.ProtoReflect.Descriptor instead.
func (*SurahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *SurahListResData) GetList() []*SuratDaftarInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SurahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() *SurahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"` // juz名称
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *JuzListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type JuzInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartSurahId   uint32 `protobuf:"varint,1,opt,name=start_surah_id,json=startSurahId,proto3" json:"start_surah_id,omitempty" dc:"开始章id"`        // 开始章id
	StartSurahName string `protobuf:"bytes,2,opt,name=start_surah_name,json=startSurahName,proto3" json:"start_surah_name,omitempty" dc:"开始章name"` // 开始章name
	EndSurahId     uint32 `protobuf:"varint,3,opt,name=end_surah_id,json=endSurahId,proto3" json:"end_surah_id,omitempty" dc:"结束章id"`              // 结束章id
	EndSurahName   string `protobuf:"bytes,4,opt,name=end_surah_name,json=endSurahName,proto3" json:"end_surah_name,omitempty" dc:"结束章name"`       // 结束章name
	StartAyahId    uint32 `protobuf:"varint,5,opt,name=start_ayah_id,json=startAyahId,proto3" json:"start_ayah_id,omitempty" dc:"开始节id"`           // 开始节id
	EndAyahId      uint32 `protobuf:"varint,6,opt,name=end_ayah_id,json=endAyahId,proto3" json:"end_ayah_id,omitempty" dc:"结束节id"`                 // 结束节id
	Juz            string `protobuf:"bytes,7,opt,name=juz,proto3" json:"juz,omitempty" dc:"juz名称"`                                                 // juz名称
	FirstWord      string `protobuf:"bytes,8,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`               // 对应经文的第一个单词
	Sort           uint32 `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty" dc:"对应经文的第一个单词"`                                         // 对应经文的第一个单词
	TempatTurun    string `protobuf:"bytes,10,opt,name=tempat_turun,json=tempatTurun,proto3" json:"tempat_turun,omitempty" dc:"朝拜方向"`              // 朝拜方向
}

func (x *JuzInfo) Reset() {
	*x = JuzInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzInfo) ProtoMessage() {}

func (x *JuzInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzInfo.ProtoReflect.Descriptor instead.
func (*JuzInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *JuzInfo) GetStartSurahId() uint32 {
	if x != nil {
		return x.StartSurahId
	}
	return 0
}

func (x *JuzInfo) GetStartSurahName() string {
	if x != nil {
		return x.StartSurahName
	}
	return ""
}

func (x *JuzInfo) GetEndSurahId() uint32 {
	if x != nil {
		return x.EndSurahId
	}
	return 0
}

func (x *JuzInfo) GetEndSurahName() string {
	if x != nil {
		return x.EndSurahName
	}
	return ""
}

func (x *JuzInfo) GetStartAyahId() uint32 {
	if x != nil {
		return x.StartAyahId
	}
	return 0
}

func (x *JuzInfo) GetEndAyahId() uint32 {
	if x != nil {
		return x.EndAyahId
	}
	return 0
}

func (x *JuzInfo) GetJuz() string {
	if x != nil {
		return x.Juz
	}
	return ""
}

func (x *JuzInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

func (x *JuzInfo) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *JuzInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

type JuzListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*JuzInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *JuzListResData) Reset() {
	*x = JuzListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListResData) ProtoMessage() {}

func (x *JuzListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListResData.ProtoReflect.Descriptor instead.
func (*JuzListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *JuzListResData) GetList() []*JuzInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type JuzListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *JuzListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() *JuzListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type AyahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                                        // 节id
	SurahId    uint32              `protobuf:"varint,2,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章节id"`              //章节id
	JuzId      uint32              `protobuf:"varint,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz_id"`                  //juz_id
	PageNumber uint32              `protobuf:"varint,4,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty" dc:"page 页数量"` //page 页数量
	Page       *common.PageRequest `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
}

func (x *AyahListReq) Reset() {
	*x = AyahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListReq) ProtoMessage() {}

func (x *AyahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListReq.ProtoReflect.Descriptor instead.
func (*AyahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{8}
}

func (x *AyahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AyahListReq) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *AyahListReq) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *AyahListReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *AyahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SuratAyatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"Ayat id"`                // Ayat id
	AyatId      uint32 `protobuf:"varint,2,opt,name=AyatId,proto3" json:"AyatId,omitempty" dc:"Ayat id"`        // Ayat id
	SurahId     uint32 `protobuf:"varint,3,opt,name=SurahId,proto3" json:"SurahId,omitempty" dc:"章 id"`         // 章 id
	Nomor       uint32 `protobuf:"varint,4,opt,name=Nomor,proto3" json:"Nomor,omitempty" dc:"章id"`              // 章id
	Tr          string `protobuf:"bytes,5,opt,name=Tr,proto3" json:"Tr,omitempty" dc:"音译文本"`                    // 音译文本
	Idn         string `protobuf:"bytes,6,opt,name=Idn,proto3" json:"Idn,omitempty" dc:"印尼语翻译"`                 // 印尼语翻译
	Ar          string `protobuf:"bytes,7,opt,name=Ar,proto3" json:"Ar,omitempty" dc:"阿拉伯语经文"`                  // 阿拉伯语经文
	Tafsir      string `protobuf:"bytes,8,opt,name=Tafsir,proto3" json:"Tafsir,omitempty" dc:"解释"`              // 解释
	Juz         uint32 `protobuf:"varint,9,opt,name=Juz,proto3" json:"Juz,omitempty" dc:"所属juz-id"`             // 所属juz-id
	Page        uint32 `protobuf:"varint,10,opt,name=Page,proto3" json:"Page,omitempty" dc:"所属page-id"`         // 所属page-id
	SurahName   string `protobuf:"bytes,11,opt,name=SurahName,proto3" json:"SurahName,omitempty" dc:"章名称"`      // 章名称
	Wajiz       string `protobuf:"bytes,12,opt,name=Wajiz,proto3" json:"Wajiz,omitempty" dc:"解释"`               // 解释
	TempatTurun string `protobuf:"bytes,13,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty" dc:"朝拜方向"` // 朝拜方向
	Audio       string `protobuf:"bytes,14,opt,name=Audio,proto3" json:"Audio,omitempty" dc:"音频地址"`             // 音频地址
}

func (x *SuratAyatInfo) Reset() {
	*x = SuratAyatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuratAyatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratAyatInfo) ProtoMessage() {}

func (x *SuratAyatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratAyatInfo.ProtoReflect.Descriptor instead.
func (*SuratAyatInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{9}
}

func (x *SuratAyatInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratAyatInfo) GetAyatId() uint32 {
	if x != nil {
		return x.AyatId
	}
	return 0
}

func (x *SuratAyatInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *SuratAyatInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratAyatInfo) GetTr() string {
	if x != nil {
		return x.Tr
	}
	return ""
}

func (x *SuratAyatInfo) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

func (x *SuratAyatInfo) GetAr() string {
	if x != nil {
		return x.Ar
	}
	return ""
}

func (x *SuratAyatInfo) GetTafsir() string {
	if x != nil {
		return x.Tafsir
	}
	return ""
}

func (x *SuratAyatInfo) GetJuz() uint32 {
	if x != nil {
		return x.Juz
	}
	return 0
}

func (x *SuratAyatInfo) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SuratAyatInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *SuratAyatInfo) GetWajiz() string {
	if x != nil {
		return x.Wajiz
	}
	return ""
}

func (x *SuratAyatInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

func (x *SuratAyatInfo) GetAudio() string {
	if x != nil {
		return x.Audio
	}
	return ""
}

type AyahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SuratAyatInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahListResData) Reset() {
	*x = AyahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListResData) ProtoMessage() {}

func (x *AyahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListResData.ProtoReflect.Descriptor instead.
func (*AyahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{10}
}

func (x *AyahListResData) GetList() []*SuratAyatInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahListRes) Reset() {
	*x = AyahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListRes) ProtoMessage() {}

func (x *AyahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListRes.ProtoReflect.Descriptor instead.
func (*AyahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{11}
}

func (x *AyahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahListRes) GetData() *AyahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId   uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                    //节id
	IsUserOp uint32 `protobuf:"varint,2,opt,name=is_user_op,json=isUserOp,proto3" json:"is_user_op,omitempty" dc:"是否用户操作，1-是，0-否"` //是否用户操作，1-是，0-否
}

func (x *AyahReadRecordReq) Reset() {
	*x = AyahReadRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordReq) ProtoMessage() {}

func (x *AyahReadRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{12}
}

func (x *AyahReadRecordReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadRecordReq) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

type AyahReadRecordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadRecordRes) Reset() {
	*x = AyahReadRecordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordRes) ProtoMessage() {}

func (x *AyahReadRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{13}
}

func (x *AyahReadRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahReadCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"`            //章节id
	IsAdd  uint32 `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` //是否添加收藏，1-添加，0-取消收藏
}

func (x *AyahReadCollectReq) Reset() {
	*x = AyahReadCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectReq) ProtoMessage() {}

func (x *AyahReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{14}
}

func (x *AyahReadCollectReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

type AyahReadCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadCollectRes) Reset() {
	*x = AyahReadCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectRes) ProtoMessage() {}

func (x *AyahReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{15}
}

func (x *AyahReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahShareReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
}

func (x *AyahShareReq) Reset() {
	*x = AyahShareReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahShareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahShareReq) ProtoMessage() {}

func (x *AyahShareReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahShareReq.ProtoReflect.Descriptor instead.
func (*AyahShareReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{16}
}

func (x *AyahShareReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type AyahShareRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahShareRes) Reset() {
	*x = AyahShareRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahShareRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahShareRes) ProtoMessage() {}

func (x *AyahShareRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahShareRes.ProtoReflect.Descriptor instead.
func (*AyahShareRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{17}
}

func (x *AyahShareRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahShareRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahShareRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckAyahReadCollectStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
}

func (x *CheckAyahReadCollectStatusReq) Reset() {
	*x = CheckAyahReadCollectStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusReq) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{18}
}

func (x *CheckAyahReadCollectStatusReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type CheckAyahReadCollectStatusResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCollect int32 `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
}

func (x *CheckAyahReadCollectStatusResData) Reset() {
	*x = CheckAyahReadCollectStatusResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusResData) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusResData.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{19}
}

func (x *CheckAyahReadCollectStatusResData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type CheckAyahReadCollectStatusRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *CheckAyahReadCollectStatusResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckAyahReadCollectStatusRes) Reset() {
	*x = CheckAyahReadCollectStatusRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusRes) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{20}
}

func (x *CheckAyahReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckAyahReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckAyahReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckAyahReadCollectStatusRes) GetData() *CheckAyahReadCollectStatusResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurahId     uint32 `protobuf:"varint,1,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章id"`               // 章id
	SurahName   string `protobuf:"bytes,2,opt,name=surah_name,json=surahName,proto3" json:"surah_name,omitempty" dc:"章name"`        // 章name
	AyahId      uint32 `protobuf:"varint,3,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                  // 节id
	JuzId       uint32 `protobuf:"varint,4,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz-id"`                  // juz-id
	Arti        string `protobuf:"bytes,5,opt,name=arti,proto3" json:"arti,omitempty" dc:"章节含义"`                                    // 章节含义
	Ayahs       uint32 `protobuf:"varint,6,opt,name=ayahs,proto3" json:"ayahs,omitempty" dc:"ayah数量"`                               // ayah数量
	FirstWord   string `protobuf:"bytes,7,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`   // 对应经文的第一个单词
	Ar          string `protobuf:"bytes,8,opt,name=ar,proto3" json:"ar,omitempty" dc:"阿拉伯语经文"`                                      // 阿拉伯语经文
	Tr          string `protobuf:"bytes,9,opt,name=tr,proto3" json:"tr,omitempty" dc:"音译文本"`                                        // 音译文本
	Idn         string `protobuf:"bytes,10,opt,name=idn,proto3" json:"idn,omitempty" dc:"印尼语翻译"`                                    // 印尼语翻译
	CollectTime int64  `protobuf:"varint,11,opt,name=collect_time,json=collectTime,proto3" json:"collect_time,omitempty" dc:"收藏时间"` // 收藏时间
	Nomor       uint32 `protobuf:"varint,12,opt,name=nomor,proto3" json:"nomor,omitempty" dc:"经文在章节中的编号"`                           // 经文在章节中的编号
}

func (x *ReadInfo) Reset() {
	*x = ReadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInfo) ProtoMessage() {}

func (x *ReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInfo.ProtoReflect.Descriptor instead.
func (*ReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{21}
}

func (x *ReadInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *ReadInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *ReadInfo) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *ReadInfo) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *ReadInfo) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *ReadInfo) GetAyahs() uint32 {
	if x != nil {
		return x.Ayahs
	}
	return 0
}

func (x *ReadInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

func (x *ReadInfo) GetAr() string {
	if x != nil {
		return x.Ar
	}
	return ""
}

func (x *ReadInfo) GetTr() string {
	if x != nil {
		return x.Tr
	}
	return ""
}

func (x *ReadInfo) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

func (x *ReadInfo) GetCollectTime() int64 {
	if x != nil {
		return x.CollectTime
	}
	return 0
}

func (x *ReadInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

type AyahReadRecordListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadRecordListReq) Reset() {
	*x = AyahReadRecordListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListReq) ProtoMessage() {}

func (x *AyahReadRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{22}
}

func (x *AyahReadRecordListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadRecordListResData) Reset() {
	*x = AyahReadRecordListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListResData) ProtoMessage() {}

func (x *AyahReadRecordListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListResData.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{23}
}

func (x *AyahReadRecordListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadRecordListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadRecordListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadRecordListRes) Reset() {
	*x = AyahReadRecordListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListRes) ProtoMessage() {}

func (x *AyahReadRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{24}
}

func (x *AyahReadRecordListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadRecordListRes) GetData() *AyahReadRecordListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadCollectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadCollectListReq) Reset() {
	*x = AyahReadCollectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListReq) ProtoMessage() {}

func (x *AyahReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{25}
}

func (x *AyahReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadCollectListResData) Reset() {
	*x = AyahReadCollectListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListResData) ProtoMessage() {}

func (x *AyahReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListResData.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{26}
}

func (x *AyahReadCollectListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadCollectListRes) Reset() {
	*x = AyahReadCollectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListRes) ProtoMessage() {}

func (x *AyahReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{27}
}

func (x *AyahReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadCollectListRes) GetData() *AyahReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TahlilListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *TahlilListReq) Reset() {
	*x = TahlilListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListReq) ProtoMessage() {}

func (x *TahlilListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListReq.ProtoReflect.Descriptor instead.
func (*TahlilListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{28}
}

func (x *TahlilListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsTahlilInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"章id"`              // 章id
	Name     string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"章name"`         // 章name
	Content1 string `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"章name"` // 章name
	Content2 string `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"章name"` // 章name
	Content3 string `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"章name"` // 章name
}

func (x *NewsTahlilInfo) Reset() {
	*x = NewsTahlilInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTahlilInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTahlilInfo) ProtoMessage() {}

func (x *NewsTahlilInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTahlilInfo.ProtoReflect.Descriptor instead.
func (*NewsTahlilInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{29}
}

func (x *NewsTahlilInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsTahlilInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type TahlilListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*NewsTahlilInfo    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *TahlilListResData) Reset() {
	*x = TahlilListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListResData) ProtoMessage() {}

func (x *TahlilListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListResData.ProtoReflect.Descriptor instead.
func (*TahlilListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{30}
}

func (x *TahlilListResData) GetList() []*NewsTahlilInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TahlilListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type TahlilListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *TahlilListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TahlilListRes) Reset() {
	*x = TahlilListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListRes) ProtoMessage() {}

func (x *TahlilListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListRes.ProtoReflect.Descriptor instead.
func (*TahlilListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{31}
}

func (x *TahlilListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TahlilListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TahlilListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *TahlilListRes) GetData() *TahlilListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type WiridListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name string              `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"` // name
}

func (x *WiridListReq) Reset() {
	*x = WiridListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListReq) ProtoMessage() {}

func (x *WiridListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListReq.ProtoReflect.Descriptor instead.
func (*WiridListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{32}
}

func (x *WiridListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                  // id
	Name    string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`             // name
	Bacaans uint32 `protobuf:"varint,3,opt,name=Bacaans,proto3" json:"Bacaans,omitempty" dc:"Bacaans数量"` // Bacaans数量
}

func (x *WiridInfo) Reset() {
	*x = WiridInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridInfo) ProtoMessage() {}

func (x *WiridInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridInfo.ProtoReflect.Descriptor instead.
func (*WiridInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{33}
}

func (x *WiridInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WiridInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WiridInfo) GetBacaans() uint32 {
	if x != nil {
		return x.Bacaans
	}
	return 0
}

type WiridListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WiridInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *WiridListResData) Reset() {
	*x = WiridListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListResData) ProtoMessage() {}

func (x *WiridListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListResData.ProtoReflect.Descriptor instead.
func (*WiridListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{34}
}

func (x *WiridListResData) GetList() []*WiridInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *WiridListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WiridListRes) Reset() {
	*x = WiridListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListRes) ProtoMessage() {}

func (x *WiridListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListRes.ProtoReflect.Descriptor instead.
func (*WiridListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{35}
}

func (x *WiridListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridListRes) GetData() *WiridListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanList信息列表
type WiridBacaanListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page    *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	WiridId int32               `protobuf:"varint,2,opt,name=wirid_id,json=wiridId,proto3" json:"wirid_id,omitempty"`
	Name    string              `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"名称"` // 名称
}

func (x *WiridBacaanListReq) Reset() {
	*x = WiridBacaanListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListReq) ProtoMessage() {}

func (x *WiridBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListReq.ProtoReflect.Descriptor instead.
func (*WiridBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{36}
}

func (x *WiridBacaanListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridBacaanListReq) GetWiridId() int32 {
	if x != nil {
		return x.WiridId
	}
	return 0
}

func (x *WiridBacaanListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridBacaanList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number   uint32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty" dc:"id"`     // id
	BacaanId uint32 `protobuf:"varint,2,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"id"` // id
	Pid      uint32 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`          // 父id
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`          // 名称
}

func (x *WiridBacaanList) Reset() {
	*x = WiridBacaanList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanList) ProtoMessage() {}

func (x *WiridBacaanList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanList.ProtoReflect.Descriptor instead.
func (*WiridBacaanList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{37}
}

func (x *WiridBacaanList) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *WiridBacaanList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *WiridBacaanList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *WiridBacaanList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridBacaanListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WiridBacaanList   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *WiridBacaanListResData) Reset() {
	*x = WiridBacaanListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListResData) ProtoMessage() {}

func (x *WiridBacaanListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListResData.ProtoReflect.Descriptor instead.
func (*WiridBacaanListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{38}
}

func (x *WiridBacaanListResData) GetList() []*WiridBacaanList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridBacaanListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridBacaanListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *WiridBacaanListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WiridBacaanListRes) Reset() {
	*x = WiridBacaanListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListRes) ProtoMessage() {}

func (x *WiridBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListRes.ProtoReflect.Descriptor instead.
func (*WiridBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{39}
}

func (x *WiridBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridBacaanListRes) GetData() *WiridBacaanListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanInfo 信息列表
type WiridBacaanInfoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	BacaanId int32               `protobuf:"varint,2,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty"`
}

func (x *WiridBacaanInfoListReq) Reset() {
	*x = WiridBacaanInfoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListReq) ProtoMessage() {}

func (x *WiridBacaanInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListReq.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{40}
}

func (x *WiridBacaanInfoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridBacaanInfoListReq) GetBacaanId() int32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

type WiridBacaanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                // id
	Name     string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`           // name
	Content1 string `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"page参数"` // page参数
	Content2 string `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"经文"`     // 经文
	Content3 string `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"解释"`     // 解释
}

func (x *WiridBacaanInfo) Reset() {
	*x = WiridBacaanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfo) ProtoMessage() {}

func (x *WiridBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfo.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{41}
}

func (x *WiridBacaanInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WiridBacaanInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type WiridBacaanInfoListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WiridBacaanInfo   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *WiridBacaanInfoListResData) Reset() {
	*x = WiridBacaanInfoListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanInfoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListResData) ProtoMessage() {}

func (x *WiridBacaanInfoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListResData.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{42}
}

func (x *WiridBacaanInfoListResData) GetList() []*WiridBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridBacaanInfoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridBacaanInfoListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *WiridBacaanInfoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WiridBacaanInfoListRes) Reset() {
	*x = WiridBacaanInfoListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiridBacaanInfoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListRes) ProtoMessage() {}

func (x *WiridBacaanInfoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListRes.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{43}
}

func (x *WiridBacaanInfoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridBacaanInfoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridBacaanInfoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridBacaanInfoListRes) GetData() *WiridBacaanInfoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name string              `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"` // name
}

func (x *DoaListReq) Reset() {
	*x = DoaListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListReq) ProtoMessage() {}

func (x *DoaListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListReq.ProtoReflect.Descriptor instead.
func (*DoaListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{44}
}

func (x *DoaListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                  // id
	Name    string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`             // name
	Bacaans uint32 `protobuf:"varint,3,opt,name=Bacaans,proto3" json:"Bacaans,omitempty" dc:"Bacaans数量"` // Bacaans数量
}

func (x *DoaInfo) Reset() {
	*x = DoaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaInfo) ProtoMessage() {}

func (x *DoaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaInfo.ProtoReflect.Descriptor instead.
func (*DoaInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{45}
}

func (x *DoaInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaInfo) GetBacaans() uint32 {
	if x != nil {
		return x.Bacaans
	}
	return 0
}

type DoaListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DoaInfo           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaListResData) Reset() {
	*x = DoaListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListResData) ProtoMessage() {}

func (x *DoaListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListResData.ProtoReflect.Descriptor instead.
func (*DoaListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{46}
}

func (x *DoaListResData) GetList() []*DoaInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *DoaListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DoaListRes) Reset() {
	*x = DoaListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListRes) ProtoMessage() {}

func (x *DoaListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListRes.ProtoReflect.Descriptor instead.
func (*DoaListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{47}
}

func (x *DoaListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaListRes) GetData() *DoaListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanList信息列表
type DoaBacaanListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	DoaId int32               `protobuf:"varint,2,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty"`
	Name  string              `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"名称"` // 名称
}

func (x *DoaBacaanListReq) Reset() {
	*x = DoaBacaanListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListReq) ProtoMessage() {}

func (x *DoaBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListReq.ProtoReflect.Descriptor instead.
func (*DoaBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{48}
}

func (x *DoaBacaanListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaBacaanListReq) GetDoaId() int32 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *DoaBacaanListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaBacaanList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number   uint32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty" dc:"id"`     // id
	BacaanId uint32 `protobuf:"varint,2,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"id"` // id
	Pid      uint32 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`          // 父id
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`          // 名称
}

func (x *DoaBacaanList) Reset() {
	*x = DoaBacaanList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanList) ProtoMessage() {}

func (x *DoaBacaanList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanList.ProtoReflect.Descriptor instead.
func (*DoaBacaanList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{49}
}

func (x *DoaBacaanList) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *DoaBacaanList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *DoaBacaanList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DoaBacaanList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaBacaanListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DoaBacaanList     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaBacaanListResData) Reset() {
	*x = DoaBacaanListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListResData) ProtoMessage() {}

func (x *DoaBacaanListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListResData.ProtoReflect.Descriptor instead.
func (*DoaBacaanListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{50}
}

func (x *DoaBacaanListResData) GetList() []*DoaBacaanList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaBacaanListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaBacaanListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *DoaBacaanListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DoaBacaanListRes) Reset() {
	*x = DoaBacaanListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListRes) ProtoMessage() {}

func (x *DoaBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListRes.ProtoReflect.Descriptor instead.
func (*DoaBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{51}
}

func (x *DoaBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaBacaanListRes) GetData() *DoaBacaanListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// Doa&wirid search 首页 信息列表
type DoaWiridSearchListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`   // 名称
}

func (x *DoaWiridSearchListReq) Reset() {
	*x = DoaWiridSearchListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaWiridSearchListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListReq) ProtoMessage() {}

func (x *DoaWiridSearchListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListReq.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{52}
}

func (x *DoaWiridSearchListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaWiridSearchListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaWiridSearchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BacaanId   uint32 `protobuf:"varint,1,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"bacaanId"`        // bacaanId
	BacaanName string `protobuf:"bytes,2,opt,name=bacaanName,proto3" json:"bacaanName,omitempty" dc:"bacaanName"`   // bacaanName
	Pid        uint32 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`                       // 父id
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                       // 名称
	Types      uint32 `protobuf:"varint,5,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid --废弃"` // 类型，1-doa，2-wirid --废弃
}

func (x *DoaWiridSearchList) Reset() {
	*x = DoaWiridSearchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaWiridSearchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchList) ProtoMessage() {}

func (x *DoaWiridSearchList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchList.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{53}
}

func (x *DoaWiridSearchList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *DoaWiridSearchList) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *DoaWiridSearchList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DoaWiridSearchList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaWiridSearchList) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaWiridSearchListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DoaWiridSearchList `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse  `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaWiridSearchListResData) Reset() {
	*x = DoaWiridSearchListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaWiridSearchListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListResData) ProtoMessage() {}

func (x *DoaWiridSearchListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListResData.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{54}
}

func (x *DoaWiridSearchListResData) GetList() []*DoaWiridSearchList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaWiridSearchListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaWiridSearchListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *DoaWiridSearchListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DoaWiridSearchListRes) Reset() {
	*x = DoaWiridSearchListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaWiridSearchListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListRes) ProtoMessage() {}

func (x *DoaWiridSearchListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListRes.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{55}
}

func (x *DoaWiridSearchListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaWiridSearchListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaWiridSearchListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaWiridSearchListRes) GetData() *DoaWiridSearchListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanInfo 信息列表
type DoaBacaanInfoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	BacaanId int32               `protobuf:"varint,2,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty"`
}

func (x *DoaBacaanInfoListReq) Reset() {
	*x = DoaBacaanInfoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListReq) ProtoMessage() {}

func (x *DoaBacaanInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListReq.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{56}
}

func (x *DoaBacaanInfoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaBacaanInfoListReq) GetBacaanId() int32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

type DoaBacaanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                // id
	Name     string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`           // name
	Content1 string `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"page参数"` // page参数
	Content2 string `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"经文"`     // 经文
	Content3 string `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"解释"`     // 解释
}

func (x *DoaBacaanInfo) Reset() {
	*x = DoaBacaanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfo) ProtoMessage() {}

func (x *DoaBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfo.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{57}
}

func (x *DoaBacaanInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaBacaanInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type DoaBacaanInfoListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DoaBacaanInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaBacaanInfoListResData) Reset() {
	*x = DoaBacaanInfoListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanInfoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListResData) ProtoMessage() {}

func (x *DoaBacaanInfoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListResData.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{58}
}

func (x *DoaBacaanInfoListResData) GetList() []*DoaBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaBacaanInfoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaBacaanInfoListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *DoaBacaanInfoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DoaBacaanInfoListRes) Reset() {
	*x = DoaBacaanInfoListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaBacaanInfoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListRes) ProtoMessage() {}

func (x *DoaBacaanInfoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListRes.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{59}
}

func (x *DoaBacaanInfoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaBacaanInfoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaBacaanInfoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaBacaanInfoListRes) GetData() *DoaBacaanInfoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaReadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaccanId uint32 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id"` //子id
	Types    uint32 `protobuf:"varint,2,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`          //类型，1-doa，2-wirid
}

func (x *DoaReadReq) Reset() {
	*x = DoaReadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadReq) ProtoMessage() {}

func (x *DoaReadReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadReq.ProtoReflect.Descriptor instead.
func (*DoaReadReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{60}
}

func (x *DoaReadReq) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *DoaReadReq) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaReadRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DoaReadRes) Reset() {
	*x = DoaReadRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadRes) ProtoMessage() {}

func (x *DoaReadRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadRes.ProtoReflect.Descriptor instead.
func (*DoaReadRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{61}
}

func (x *DoaReadRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaReadRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaReadRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type DoaReadCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaccanId uint32 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id"` //子id
	Types    uint32 `protobuf:"varint,2,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`          //类型，1-doa，2-wirid
}

func (x *DoaReadCollectReq) Reset() {
	*x = DoaReadCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectReq) ProtoMessage() {}

func (x *DoaReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectReq.ProtoReflect.Descriptor instead.
func (*DoaReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{62}
}

func (x *DoaReadCollectReq) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *DoaReadCollectReq) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaReadCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DoaReadCollectRes) Reset() {
	*x = DoaReadCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectRes) ProtoMessage() {}

func (x *DoaReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectRes.ProtoReflect.Descriptor instead.
func (*DoaReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{63}
}

func (x *DoaReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckDoaReadCollectStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaccanId uint32 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id"` //子id
	Types    uint32 `protobuf:"varint,2,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`          //类型，1-doa，2-wirid
}

func (x *CheckDoaReadCollectStatusReq) Reset() {
	*x = CheckDoaReadCollectStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDoaReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusReq) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{64}
}

func (x *CheckDoaReadCollectStatusReq) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *CheckDoaReadCollectStatusReq) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type CheckDoaReadCollectStatusResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCollect int32 `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
}

func (x *CheckDoaReadCollectStatusResData) Reset() {
	*x = CheckDoaReadCollectStatusResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDoaReadCollectStatusResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusResData) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusResData.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{65}
}

func (x *CheckDoaReadCollectStatusResData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type CheckDoaReadCollectStatusRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *CheckDoaReadCollectStatusResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckDoaReadCollectStatusRes) Reset() {
	*x = CheckDoaReadCollectStatusRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDoaReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusRes) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{66}
}

func (x *CheckDoaReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckDoaReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckDoaReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckDoaReadCollectStatusRes) GetData() *CheckDoaReadCollectStatusResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaReadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaccanId   uint32 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id- sub-category id"`       // 子id- sub-category id
	BaccanName string `protobuf:"bytes,2,opt,name=baccan_name,json=baccanName,proto3" json:"baccan_name,omitempty" dc:"子name sub-category id"` // 子name sub-category id
	PName      string `protobuf:"bytes,3,opt,name=p_name,json=pName,proto3" json:"p_name,omitempty" dc:"父名称  category id"`                     // 父名称  category id
	Types      uint32 `protobuf:"varint,4,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`                                 // 类型，1-doa，2-wirid
}

func (x *DoaReadInfo) Reset() {
	*x = DoaReadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadInfo) ProtoMessage() {}

func (x *DoaReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadInfo.ProtoReflect.Descriptor instead.
func (*DoaReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{67}
}

func (x *DoaReadInfo) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *DoaReadInfo) GetBaccanName() string {
	if x != nil {
		return x.BaccanName
	}
	return ""
}

func (x *DoaReadInfo) GetPName() string {
	if x != nil {
		return x.PName
	}
	return ""
}

func (x *DoaReadInfo) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaReadCollectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaReadCollectListReq) Reset() {
	*x = DoaReadCollectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListReq) ProtoMessage() {}

func (x *DoaReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListReq.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{68}
}

func (x *DoaReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaReadCollectListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DoaReadInfo       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *DoaReadCollectListResData) Reset() {
	*x = DoaReadCollectListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListResData) ProtoMessage() {}

func (x *DoaReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListResData.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{69}
}

func (x *DoaReadCollectListResData) GetList() []*DoaReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaReadCollectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *DoaReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DoaReadCollectListRes) Reset() {
	*x = DoaReadCollectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoaReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListRes) ProtoMessage() {}

func (x *DoaReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListRes.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{70}
}

func (x *DoaReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaReadCollectListRes) GetData() *DoaReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

var file_islamic_v1_surah_proto_rawDesc = []byte{
	0x0a, 0x16, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x61, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73,
	0x75, 0x72, 0x61, 0x74, 0x5f, 0x61, 0x79, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f,
	0x64, 0x61, 0x66, 0x74, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f, 0x74, 0x61, 0x66,
	0x73, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x74, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x7a, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x70, 0x75,
	0x6c, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69, 0x73, 0x50, 0x6f, 0x70,
	0x75, 0x6c, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xf3, 0x01,
	0x0a, 0x0f, 0x53, 0x75, 0x72, 0x61, 0x74, 0x44, 0x61, 0x66, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x4e,
	0x61, 0x6d, 0x61, 0x4c, 0x61, 0x74, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x4e, 0x61, 0x6d, 0x61, 0x4c, 0x61, 0x74, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x4a, 0x75, 0x6d,
	0x6c, 0x61, 0x68, 0x41, 0x79, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x4a,
	0x75, 0x6d, 0x6c, 0x61, 0x68, 0x41, 0x79, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d,
	0x70, 0x61, 0x74, 0x54, 0x75, 0x72, 0x75, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x54, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75, 0x72, 0x75, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x41,
	0x72, 0x74, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x41, 0x72, 0x74, 0x69, 0x12,
	0x1c, 0x0a, 0x09, 0x44, 0x65, 0x73, 0x6b, 0x72, 0x69, 0x70, 0x73, 0x69, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x73, 0x6b, 0x72, 0x69, 0x70, 0x73, 0x69, 0x12, 0x14, 0x0a,
	0x05, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x41, 0x75,
	0x64, 0x69, 0x6f, 0x22, 0x6d, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x74, 0x44, 0x61, 0x66, 0x74, 0x61, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x30,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x20, 0x0a, 0x0a, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xcd, 0x02, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24,
	0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x72,
	0x61, 0x68, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75,
	0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x53, 0x75, 0x72,
	0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x65, 0x6e,
	0x64, 0x5f, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x65, 0x6e, 0x64, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6a, 0x75,
	0x7a, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x75, 0x7a, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x5f, 0x74, 0x75, 0x72, 0x75, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75, 0x72,
	0x75, 0x6e, 0x22, 0x39, 0x0a, 0x0e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x75, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x87, 0x01,
	0x0a, 0x0a, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x75, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6a, 0x75, 0x7a, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0xc3, 0x02, 0x0a, 0x0d, 0x53, 0x75, 0x72, 0x61, 0x74, 0x41, 0x79, 0x61,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x79, 0x61, 0x74, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x41, 0x79, 0x61, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x4e, 0x6f, 0x6d, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x54, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x54, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x49, 0x64, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x49, 0x64, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x41, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x41, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x54, 0x61, 0x66, 0x73, 0x69, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x54, 0x61, 0x66, 0x73, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x4a, 0x75, 0x7a, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x4a, 0x75, 0x7a, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x57,
	0x61, 0x6a, 0x69, 0x7a, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x57, 0x61, 0x6a, 0x69,
	0x7a, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75, 0x72, 0x75, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75,
	0x72, 0x75, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x22, 0x6a, 0x0a, 0x0f, 0x41, 0x79, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x74, 0x41, 0x79, 0x61,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x4a, 0x0a, 0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x70, 0x22, 0x5e, 0x0a,
	0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x44, 0x0a,
	0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x69, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x73,
	0x41, 0x64, 0x64, 0x22, 0x5f, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0x27, 0x0a, 0x0c, 0x41, 0x79, 0x61, 0x68, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x22, 0x59, 0x0a,
	0x0c, 0x41, 0x79, 0x61, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x38, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68,
	0x49, 0x64, 0x22, 0x42, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x22, 0xad, 0x01, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa8, 0x02, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x75, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6a, 0x75, 0x7a, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x72, 0x74, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x74,
	0x69, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x79, 0x61, 0x68, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x61, 0x79, 0x61, 0x68, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x61, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e,
	0x6f, 0x6d, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6e, 0x6f, 0x6d, 0x6f,
	0x72, 0x22, 0x40, 0x0a, 0x15, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0x6f, 0x0a, 0x19, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x16, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x70, 0x0a, 0x1a, 0x41, 0x79, 0x61, 0x68, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x41, 0x79,
	0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x38, 0x0a, 0x0d, 0x54,
	0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x61,
	0x68, 0x6c, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x33,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x33,
	0x22, 0x6d, 0x0a, 0x11, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22,
	0x8d, 0x01, 0x0a, 0x0d, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x4b, 0x0a, 0x0c, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x09,
	0x57, 0x69, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x73, 0x22, 0x67, 0x0a, 0x10, 0x57, 0x69, 0x72, 0x69, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6c,
	0x0a, 0x12, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x77, 0x69, 0x72, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x77, 0x69, 0x72, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6b, 0x0a, 0x0f,
	0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x63, 0x61, 0x61,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61, 0x63, 0x61, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x73, 0x0a, 0x16, 0x57, 0x69, 0x72,
	0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x97,
	0x01, 0x0a, 0x12, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69,
	0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5e, 0x0a, 0x16, 0x57, 0x69, 0x72, 0x69,
	0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x63, 0x61, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x0f, 0x57, 0x69, 0x72,
	0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x12, 0x1a, 0x0a, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x33, 0x22, 0x77, 0x0a, 0x1a, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69,
	0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9f, 0x01,
	0x0a, 0x16, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x49, 0x0a, 0x0a, 0x44, 0x6f, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x47, 0x0a, 0x07, 0x44, 0x6f,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x42, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x73, 0x22, 0x63, 0x0a, 0x0e, 0x44, 0x6f, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x6f, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x0a, 0x44, 0x6f, 0x61,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x66, 0x0a, 0x10, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x64, 0x6f, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x64, 0x6f, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x69, 0x0a, 0x0d, 0x44, 0x6f,
	0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6f, 0x0a, 0x14, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x10, 0x44, 0x6f, 0x61, 0x42, 0x61,
	0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x15,
	0x44, 0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x44, 0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x61, 0x61,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x22, 0x79, 0x0a, 0x19, 0x44, 0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x57, 0x69, 0x72,
	0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x01, 0x0a,
	0x15, 0x44, 0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61,
	0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5c, 0x0a, 0x14,
	0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x62, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x44,
	0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x31, 0x12, 0x1a, 0x0a, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x33, 0x22, 0x73, 0x0a, 0x18, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42,
	0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x14, 0x44, 0x6f,
	0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x38, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3f, 0x0a, 0x0a, 0x44, 0x6f, 0x61, 0x52, 0x65,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63, 0x63, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61, 0x63, 0x63, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x57, 0x0a, 0x0a, 0x44, 0x6f, 0x61, 0x52,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x22, 0x46, 0x0a, 0x11, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63, 0x63, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61, 0x63, 0x63, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x5e, 0x0a, 0x11, 0x44, 0x6f, 0x61,
	0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x51, 0x0a, 0x1c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63,
	0x63, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x61,
	0x63, 0x63, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x41, 0x0a, 0x20,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x22,
	0xab, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x40, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x78, 0x0a,
	0x0b, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x61, 0x63, 0x63, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x62, 0x61, 0x63, 0x63, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x63,
	0x63, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x62, 0x61, 0x63, 0x63, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x40, 0x0a, 0x15, 0x44, 0x6f, 0x61, 0x52, 0x65,
	0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x19, 0x44, 0x6f, 0x61,
	0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x01,
	0x0a, 0x15, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f,
	0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xa7, 0x0d,
	0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f,
	0x0a, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x39, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x41, 0x79,
	0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x41, 0x79, 0x61, 0x68,
	0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68,
	0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68,
	0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x72, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x29, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x41, 0x79, 0x61, 0x68,
	0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x41,
	0x79, 0x61, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x79, 0x61, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x13,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x54,
	0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x3f, 0x0a, 0x09, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x51, 0x0a, 0x0f, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x13, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63, 0x61,
	0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69, 0x64, 0x42, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x72, 0x69,
	0x64, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x44, 0x6f, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a,
	0x0d, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42,
	0x61, 0x63, 0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x42, 0x61, 0x63,
	0x61, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x11, 0x44, 0x6f,
	0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x20, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61,
	0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x61, 0x42, 0x61, 0x63, 0x61, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x6f, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x28, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x6f, 0x61,
	0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x12,
	0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61,
	0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x12,
	0x4e, 0x0a, 0x0e, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x12, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f,
	0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x5a, 0x0a, 0x12, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x61, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x12, 0x44,
	0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x6f, 0x61, 0x57, 0x69, 0x72, 0x69, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c,
	0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData = file_islamic_v1_surah_proto_rawDesc
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_surah_proto_rawDescData)
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_islamic_v1_surah_proto_goTypes = []interface{}{
	(*SurahListReq)(nil),                      // 0: islamic.v1.SurahListReq
	(*SuratDaftarInfo)(nil),                   // 1: islamic.v1.SuratDaftarInfo
	(*SurahListResData)(nil),                  // 2: islamic.v1.SurahListResData
	(*SurahListRes)(nil),                      // 3: islamic.v1.SurahListRes
	(*JuzListReq)(nil),                        // 4: islamic.v1.JuzListReq
	(*JuzInfo)(nil),                           // 5: islamic.v1.JuzInfo
	(*JuzListResData)(nil),                    // 6: islamic.v1.JuzListResData
	(*JuzListRes)(nil),                        // 7: islamic.v1.JuzListRes
	(*AyahListReq)(nil),                       // 8: islamic.v1.AyahListReq
	(*SuratAyatInfo)(nil),                     // 9: islamic.v1.SuratAyatInfo
	(*AyahListResData)(nil),                   // 10: islamic.v1.AyahListResData
	(*AyahListRes)(nil),                       // 11: islamic.v1.AyahListRes
	(*AyahReadRecordReq)(nil),                 // 12: islamic.v1.AyahReadRecordReq
	(*AyahReadRecordRes)(nil),                 // 13: islamic.v1.AyahReadRecordRes
	(*AyahReadCollectReq)(nil),                // 14: islamic.v1.AyahReadCollectReq
	(*AyahReadCollectRes)(nil),                // 15: islamic.v1.AyahReadCollectRes
	(*AyahShareReq)(nil),                      // 16: islamic.v1.AyahShareReq
	(*AyahShareRes)(nil),                      // 17: islamic.v1.AyahShareRes
	(*CheckAyahReadCollectStatusReq)(nil),     // 18: islamic.v1.CheckAyahReadCollectStatusReq
	(*CheckAyahReadCollectStatusResData)(nil), // 19: islamic.v1.CheckAyahReadCollectStatusResData
	(*CheckAyahReadCollectStatusRes)(nil),     // 20: islamic.v1.CheckAyahReadCollectStatusRes
	(*ReadInfo)(nil),                          // 21: islamic.v1.ReadInfo
	(*AyahReadRecordListReq)(nil),             // 22: islamic.v1.AyahReadRecordListReq
	(*AyahReadRecordListResData)(nil),         // 23: islamic.v1.AyahReadRecordListResData
	(*AyahReadRecordListRes)(nil),             // 24: islamic.v1.AyahReadRecordListRes
	(*AyahReadCollectListReq)(nil),            // 25: islamic.v1.AyahReadCollectListReq
	(*AyahReadCollectListResData)(nil),        // 26: islamic.v1.AyahReadCollectListResData
	(*AyahReadCollectListRes)(nil),            // 27: islamic.v1.AyahReadCollectListRes
	(*TahlilListReq)(nil),                     // 28: islamic.v1.TahlilListReq
	(*NewsTahlilInfo)(nil),                    // 29: islamic.v1.NewsTahlilInfo
	(*TahlilListResData)(nil),                 // 30: islamic.v1.TahlilListResData
	(*TahlilListRes)(nil),                     // 31: islamic.v1.TahlilListRes
	(*WiridListReq)(nil),                      // 32: islamic.v1.WiridListReq
	(*WiridInfo)(nil),                         // 33: islamic.v1.WiridInfo
	(*WiridListResData)(nil),                  // 34: islamic.v1.WiridListResData
	(*WiridListRes)(nil),                      // 35: islamic.v1.WiridListRes
	(*WiridBacaanListReq)(nil),                // 36: islamic.v1.WiridBacaanListReq
	(*WiridBacaanList)(nil),                   // 37: islamic.v1.WiridBacaanList
	(*WiridBacaanListResData)(nil),            // 38: islamic.v1.WiridBacaanListResData
	(*WiridBacaanListRes)(nil),                // 39: islamic.v1.WiridBacaanListRes
	(*WiridBacaanInfoListReq)(nil),            // 40: islamic.v1.WiridBacaanInfoListReq
	(*WiridBacaanInfo)(nil),                   // 41: islamic.v1.WiridBacaanInfo
	(*WiridBacaanInfoListResData)(nil),        // 42: islamic.v1.WiridBacaanInfoListResData
	(*WiridBacaanInfoListRes)(nil),            // 43: islamic.v1.WiridBacaanInfoListRes
	(*DoaListReq)(nil),                        // 44: islamic.v1.DoaListReq
	(*DoaInfo)(nil),                           // 45: islamic.v1.DoaInfo
	(*DoaListResData)(nil),                    // 46: islamic.v1.DoaListResData
	(*DoaListRes)(nil),                        // 47: islamic.v1.DoaListRes
	(*DoaBacaanListReq)(nil),                  // 48: islamic.v1.DoaBacaanListReq
	(*DoaBacaanList)(nil),                     // 49: islamic.v1.DoaBacaanList
	(*DoaBacaanListResData)(nil),              // 50: islamic.v1.DoaBacaanListResData
	(*DoaBacaanListRes)(nil),                  // 51: islamic.v1.DoaBacaanListRes
	(*DoaWiridSearchListReq)(nil),             // 52: islamic.v1.DoaWiridSearchListReq
	(*DoaWiridSearchList)(nil),                // 53: islamic.v1.DoaWiridSearchList
	(*DoaWiridSearchListResData)(nil),         // 54: islamic.v1.DoaWiridSearchListResData
	(*DoaWiridSearchListRes)(nil),             // 55: islamic.v1.DoaWiridSearchListRes
	(*DoaBacaanInfoListReq)(nil),              // 56: islamic.v1.DoaBacaanInfoListReq
	(*DoaBacaanInfo)(nil),                     // 57: islamic.v1.DoaBacaanInfo
	(*DoaBacaanInfoListResData)(nil),          // 58: islamic.v1.DoaBacaanInfoListResData
	(*DoaBacaanInfoListRes)(nil),              // 59: islamic.v1.DoaBacaanInfoListRes
	(*DoaReadReq)(nil),                        // 60: islamic.v1.DoaReadReq
	(*DoaReadRes)(nil),                        // 61: islamic.v1.DoaReadRes
	(*DoaReadCollectReq)(nil),                 // 62: islamic.v1.DoaReadCollectReq
	(*DoaReadCollectRes)(nil),                 // 63: islamic.v1.DoaReadCollectRes
	(*CheckDoaReadCollectStatusReq)(nil),      // 64: islamic.v1.CheckDoaReadCollectStatusReq
	(*CheckDoaReadCollectStatusResData)(nil),  // 65: islamic.v1.CheckDoaReadCollectStatusResData
	(*CheckDoaReadCollectStatusRes)(nil),      // 66: islamic.v1.CheckDoaReadCollectStatusRes
	(*DoaReadInfo)(nil),                       // 67: islamic.v1.DoaReadInfo
	(*DoaReadCollectListReq)(nil),             // 68: islamic.v1.DoaReadCollectListReq
	(*DoaReadCollectListResData)(nil),         // 69: islamic.v1.DoaReadCollectListResData
	(*DoaReadCollectListRes)(nil),             // 70: islamic.v1.DoaReadCollectListRes
	(*common.PageRequest)(nil),                // 71: common.PageRequest
	(*common.PageResponse)(nil),               // 72: common.PageResponse
	(*common.Error)(nil),                      // 73: common.Error
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	71, // 0: islamic.v1.SurahListReq.page:type_name -> common.PageRequest
	1,  // 1: islamic.v1.SurahListResData.list:type_name -> islamic.v1.SuratDaftarInfo
	72, // 2: islamic.v1.SurahListResData.page:type_name -> common.PageResponse
	73, // 3: islamic.v1.SurahListRes.error:type_name -> common.Error
	2,  // 4: islamic.v1.SurahListRes.data:type_name -> islamic.v1.SurahListResData
	5,  // 5: islamic.v1.JuzListResData.list:type_name -> islamic.v1.JuzInfo
	73, // 6: islamic.v1.JuzListRes.error:type_name -> common.Error
	6,  // 7: islamic.v1.JuzListRes.data:type_name -> islamic.v1.JuzListResData
	71, // 8: islamic.v1.AyahListReq.page:type_name -> common.PageRequest
	9,  // 9: islamic.v1.AyahListResData.list:type_name -> islamic.v1.SuratAyatInfo
	72, // 10: islamic.v1.AyahListResData.page:type_name -> common.PageResponse
	73, // 11: islamic.v1.AyahListRes.error:type_name -> common.Error
	10, // 12: islamic.v1.AyahListRes.data:type_name -> islamic.v1.AyahListResData
	73, // 13: islamic.v1.AyahReadRecordRes.error:type_name -> common.Error
	73, // 14: islamic.v1.AyahReadCollectRes.error:type_name -> common.Error
	73, // 15: islamic.v1.AyahShareRes.error:type_name -> common.Error
	73, // 16: islamic.v1.CheckAyahReadCollectStatusRes.error:type_name -> common.Error
	19, // 17: islamic.v1.CheckAyahReadCollectStatusRes.data:type_name -> islamic.v1.CheckAyahReadCollectStatusResData
	71, // 18: islamic.v1.AyahReadRecordListReq.page:type_name -> common.PageRequest
	21, // 19: islamic.v1.AyahReadRecordListResData.list:type_name -> islamic.v1.ReadInfo
	72, // 20: islamic.v1.AyahReadRecordListResData.page:type_name -> common.PageResponse
	73, // 21: islamic.v1.AyahReadRecordListRes.error:type_name -> common.Error
	23, // 22: islamic.v1.AyahReadRecordListRes.data:type_name -> islamic.v1.AyahReadRecordListResData
	71, // 23: islamic.v1.AyahReadCollectListReq.page:type_name -> common.PageRequest
	21, // 24: islamic.v1.AyahReadCollectListResData.list:type_name -> islamic.v1.ReadInfo
	72, // 25: islamic.v1.AyahReadCollectListResData.page:type_name -> common.PageResponse
	73, // 26: islamic.v1.AyahReadCollectListRes.error:type_name -> common.Error
	26, // 27: islamic.v1.AyahReadCollectListRes.data:type_name -> islamic.v1.AyahReadCollectListResData
	71, // 28: islamic.v1.TahlilListReq.page:type_name -> common.PageRequest
	29, // 29: islamic.v1.TahlilListResData.list:type_name -> islamic.v1.NewsTahlilInfo
	72, // 30: islamic.v1.TahlilListResData.page:type_name -> common.PageResponse
	73, // 31: islamic.v1.TahlilListRes.error:type_name -> common.Error
	30, // 32: islamic.v1.TahlilListRes.data:type_name -> islamic.v1.TahlilListResData
	71, // 33: islamic.v1.WiridListReq.page:type_name -> common.PageRequest
	33, // 34: islamic.v1.WiridListResData.list:type_name -> islamic.v1.WiridInfo
	72, // 35: islamic.v1.WiridListResData.page:type_name -> common.PageResponse
	73, // 36: islamic.v1.WiridListRes.error:type_name -> common.Error
	34, // 37: islamic.v1.WiridListRes.data:type_name -> islamic.v1.WiridListResData
	71, // 38: islamic.v1.WiridBacaanListReq.page:type_name -> common.PageRequest
	37, // 39: islamic.v1.WiridBacaanListResData.list:type_name -> islamic.v1.WiridBacaanList
	72, // 40: islamic.v1.WiridBacaanListResData.page:type_name -> common.PageResponse
	73, // 41: islamic.v1.WiridBacaanListRes.error:type_name -> common.Error
	38, // 42: islamic.v1.WiridBacaanListRes.data:type_name -> islamic.v1.WiridBacaanListResData
	71, // 43: islamic.v1.WiridBacaanInfoListReq.page:type_name -> common.PageRequest
	41, // 44: islamic.v1.WiridBacaanInfoListResData.list:type_name -> islamic.v1.WiridBacaanInfo
	72, // 45: islamic.v1.WiridBacaanInfoListResData.page:type_name -> common.PageResponse
	73, // 46: islamic.v1.WiridBacaanInfoListRes.error:type_name -> common.Error
	42, // 47: islamic.v1.WiridBacaanInfoListRes.data:type_name -> islamic.v1.WiridBacaanInfoListResData
	71, // 48: islamic.v1.DoaListReq.page:type_name -> common.PageRequest
	45, // 49: islamic.v1.DoaListResData.list:type_name -> islamic.v1.DoaInfo
	72, // 50: islamic.v1.DoaListResData.page:type_name -> common.PageResponse
	73, // 51: islamic.v1.DoaListRes.error:type_name -> common.Error
	46, // 52: islamic.v1.DoaListRes.data:type_name -> islamic.v1.DoaListResData
	71, // 53: islamic.v1.DoaBacaanListReq.page:type_name -> common.PageRequest
	49, // 54: islamic.v1.DoaBacaanListResData.list:type_name -> islamic.v1.DoaBacaanList
	72, // 55: islamic.v1.DoaBacaanListResData.page:type_name -> common.PageResponse
	73, // 56: islamic.v1.DoaBacaanListRes.error:type_name -> common.Error
	50, // 57: islamic.v1.DoaBacaanListRes.data:type_name -> islamic.v1.DoaBacaanListResData
	71, // 58: islamic.v1.DoaWiridSearchListReq.page:type_name -> common.PageRequest
	53, // 59: islamic.v1.DoaWiridSearchListResData.list:type_name -> islamic.v1.DoaWiridSearchList
	72, // 60: islamic.v1.DoaWiridSearchListResData.page:type_name -> common.PageResponse
	73, // 61: islamic.v1.DoaWiridSearchListRes.error:type_name -> common.Error
	54, // 62: islamic.v1.DoaWiridSearchListRes.data:type_name -> islamic.v1.DoaWiridSearchListResData
	71, // 63: islamic.v1.DoaBacaanInfoListReq.page:type_name -> common.PageRequest
	57, // 64: islamic.v1.DoaBacaanInfoListResData.list:type_name -> islamic.v1.DoaBacaanInfo
	72, // 65: islamic.v1.DoaBacaanInfoListResData.page:type_name -> common.PageResponse
	73, // 66: islamic.v1.DoaBacaanInfoListRes.error:type_name -> common.Error
	58, // 67: islamic.v1.DoaBacaanInfoListRes.data:type_name -> islamic.v1.DoaBacaanInfoListResData
	73, // 68: islamic.v1.DoaReadRes.error:type_name -> common.Error
	73, // 69: islamic.v1.DoaReadCollectRes.error:type_name -> common.Error
	73, // 70: islamic.v1.CheckDoaReadCollectStatusRes.error:type_name -> common.Error
	65, // 71: islamic.v1.CheckDoaReadCollectStatusRes.data:type_name -> islamic.v1.CheckDoaReadCollectStatusResData
	71, // 72: islamic.v1.DoaReadCollectListReq.page:type_name -> common.PageRequest
	67, // 73: islamic.v1.DoaReadCollectListResData.list:type_name -> islamic.v1.DoaReadInfo
	72, // 74: islamic.v1.DoaReadCollectListResData.page:type_name -> common.PageResponse
	73, // 75: islamic.v1.DoaReadCollectListRes.error:type_name -> common.Error
	69, // 76: islamic.v1.DoaReadCollectListRes.data:type_name -> islamic.v1.DoaReadCollectListResData
	0,  // 77: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	4,  // 78: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	8,  // 79: islamic.v1.SurahService.AyahList:input_type -> islamic.v1.AyahListReq
	12, // 80: islamic.v1.SurahService.AyahReadRecord:input_type -> islamic.v1.AyahReadRecordReq
	22, // 81: islamic.v1.SurahService.AyahReadRecordList:input_type -> islamic.v1.AyahReadRecordListReq
	18, // 82: islamic.v1.SurahService.CheckAyahReadCollectStatus:input_type -> islamic.v1.CheckAyahReadCollectStatusReq
	14, // 83: islamic.v1.SurahService.AyahReadCollect:input_type -> islamic.v1.AyahReadCollectReq
	16, // 84: islamic.v1.SurahService.AyahShare:input_type -> islamic.v1.AyahShareReq
	25, // 85: islamic.v1.SurahService.AyahReadCollectList:input_type -> islamic.v1.AyahReadCollectListReq
	28, // 86: islamic.v1.SurahService.TahlilList:input_type -> islamic.v1.TahlilListReq
	32, // 87: islamic.v1.SurahService.WiridList:input_type -> islamic.v1.WiridListReq
	36, // 88: islamic.v1.SurahService.WiridBacaanList:input_type -> islamic.v1.WiridBacaanListReq
	40, // 89: islamic.v1.SurahService.WiridBacaanInfoList:input_type -> islamic.v1.WiridBacaanInfoListReq
	44, // 90: islamic.v1.SurahService.DoaList:input_type -> islamic.v1.DoaListReq
	48, // 91: islamic.v1.SurahService.DoaBacaanList:input_type -> islamic.v1.DoaBacaanListReq
	56, // 92: islamic.v1.SurahService.DoaBacaanInfoList:input_type -> islamic.v1.DoaBacaanInfoListReq
	64, // 93: islamic.v1.SurahService.CheckDoaReadCollectStatus:input_type -> islamic.v1.CheckDoaReadCollectStatusReq
	60, // 94: islamic.v1.SurahService.DoaRead:input_type -> islamic.v1.DoaReadReq
	62, // 95: islamic.v1.SurahService.DoaReadCollect:input_type -> islamic.v1.DoaReadCollectReq
	68, // 96: islamic.v1.SurahService.DoaReadCollectList:input_type -> islamic.v1.DoaReadCollectListReq
	52, // 97: islamic.v1.SurahService.DoaWiridSearchList:input_type -> islamic.v1.DoaWiridSearchListReq
	3,  // 98: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	7,  // 99: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	11, // 100: islamic.v1.SurahService.AyahList:output_type -> islamic.v1.AyahListRes
	13, // 101: islamic.v1.SurahService.AyahReadRecord:output_type -> islamic.v1.AyahReadRecordRes
	24, // 102: islamic.v1.SurahService.AyahReadRecordList:output_type -> islamic.v1.AyahReadRecordListRes
	20, // 103: islamic.v1.SurahService.CheckAyahReadCollectStatus:output_type -> islamic.v1.CheckAyahReadCollectStatusRes
	15, // 104: islamic.v1.SurahService.AyahReadCollect:output_type -> islamic.v1.AyahReadCollectRes
	17, // 105: islamic.v1.SurahService.AyahShare:output_type -> islamic.v1.AyahShareRes
	27, // 106: islamic.v1.SurahService.AyahReadCollectList:output_type -> islamic.v1.AyahReadCollectListRes
	31, // 107: islamic.v1.SurahService.TahlilList:output_type -> islamic.v1.TahlilListRes
	35, // 108: islamic.v1.SurahService.WiridList:output_type -> islamic.v1.WiridListRes
	39, // 109: islamic.v1.SurahService.WiridBacaanList:output_type -> islamic.v1.WiridBacaanListRes
	43, // 110: islamic.v1.SurahService.WiridBacaanInfoList:output_type -> islamic.v1.WiridBacaanInfoListRes
	47, // 111: islamic.v1.SurahService.DoaList:output_type -> islamic.v1.DoaListRes
	51, // 112: islamic.v1.SurahService.DoaBacaanList:output_type -> islamic.v1.DoaBacaanListRes
	59, // 113: islamic.v1.SurahService.DoaBacaanInfoList:output_type -> islamic.v1.DoaBacaanInfoListRes
	66, // 114: islamic.v1.SurahService.CheckDoaReadCollectStatus:output_type -> islamic.v1.CheckDoaReadCollectStatusRes
	61, // 115: islamic.v1.SurahService.DoaRead:output_type -> islamic.v1.DoaReadRes
	63, // 116: islamic.v1.SurahService.DoaReadCollect:output_type -> islamic.v1.DoaReadCollectRes
	70, // 117: islamic.v1.SurahService.DoaReadCollectList:output_type -> islamic.v1.DoaReadCollectListRes
	55, // 118: islamic.v1.SurahService.DoaWiridSearchList:output_type -> islamic.v1.DoaWiridSearchListRes
	98, // [98:119] is the sub-list for method output_type
	77, // [77:98] is the sub-list for method input_type
	77, // [77:77] is the sub-list for extension type_name
	77, // [77:77] is the sub-list for extension extendee
	0,  // [0:77] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_surah_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuratDaftarInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuratAyatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahShareReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahShareRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTahlilInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanInfoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanInfoListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiridBacaanInfoListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaWiridSearchListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaWiridSearchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaWiridSearchListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaWiridSearchListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanInfoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanInfoListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaBacaanInfoListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDoaReadCollectStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDoaReadCollectStatusResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDoaReadCollectStatusRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadCollectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadCollectListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoaReadCollectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_surah_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_rawDesc = nil
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
