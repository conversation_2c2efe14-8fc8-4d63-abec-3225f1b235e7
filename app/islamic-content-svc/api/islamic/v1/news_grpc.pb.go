// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// NewsServiceClient is the client API for NewsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NewsServiceClient interface {
	// 新闻分类列表-done
	NewsCategoryList(ctx context.Context, in *NewsCategoryListReq, opts ...grpc.CallOption) (*NewsCategoryListRes, error)
	// 新闻列表通过分类ID-done
	NewsListByCateId(ctx context.Context, in *NewsListByCateIdReq, opts ...grpc.CallOption) (*NewsListByCateIdRes, error)
	// 新闻专题列表-done
	NewsTopicList(ctx context.Context, in *NewsTopicListReq, opts ...grpc.CallOption) (*NewsTopicListRes, error)
	// 新闻列表通过专题ID
	NewsListByTopicId(ctx context.Context, in *NewsListByTopicIdReq, opts ...grpc.CallOption) (*NewsListByTopicIdRes, error)
	// 新闻详情
	NewsInfo(ctx context.Context, in *NewsInfoReq, opts ...grpc.CallOption) (*NewsInfoRes, error)
	// 热门新闻列表
	NewsHotList(ctx context.Context, in *NewsHotListReq, opts ...grpc.CallOption) (*NewsHotListRes, error)
	// 新闻收藏列表
	NewsCollectList(ctx context.Context, in *NewsCollectReq, opts ...grpc.CallOption) (*NewsCollectRes, error)
	// 新闻收藏状态检查
	NewsCollectStatusCheck(ctx context.Context, in *NewsCollectStatusCheckReq, opts ...grpc.CallOption) (*NewsCollectStatusCheckRes, error)
	// 新闻收藏操作
	NewsCollectOp(ctx context.Context, in *NewsCollectOpReq, opts ...grpc.CallOption) (*NewsCollectOpRes, error)
	// 新闻分享操作
	NewsShareOp(ctx context.Context, in *NewsShareOpReq, opts ...grpc.CallOption) (*NewsShareOpRes, error)
	// 新闻浏览操作
	NewsViewOp(ctx context.Context, in *NewsViewOpReq, opts ...grpc.CallOption) (*NewsViewOpRes, error)
	// 用户书签信息
	UserBookmark(ctx context.Context, in *UserBookmarkReq, opts ...grpc.CallOption) (*UserBookmarkRes, error)
}

type newsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNewsServiceClient(cc grpc.ClientConnInterface) NewsServiceClient {
	return &newsServiceClient{cc}
}

func (c *newsServiceClient) NewsCategoryList(ctx context.Context, in *NewsCategoryListReq, opts ...grpc.CallOption) (*NewsCategoryListRes, error) {
	out := new(NewsCategoryListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsListByCateId(ctx context.Context, in *NewsListByCateIdReq, opts ...grpc.CallOption) (*NewsListByCateIdRes, error) {
	out := new(NewsListByCateIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsListByCateId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsTopicList(ctx context.Context, in *NewsTopicListReq, opts ...grpc.CallOption) (*NewsTopicListRes, error) {
	out := new(NewsTopicListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsListByTopicId(ctx context.Context, in *NewsListByTopicIdReq, opts ...grpc.CallOption) (*NewsListByTopicIdRes, error) {
	out := new(NewsListByTopicIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsListByTopicId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsInfo(ctx context.Context, in *NewsInfoReq, opts ...grpc.CallOption) (*NewsInfoRes, error) {
	out := new(NewsInfoRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsHotList(ctx context.Context, in *NewsHotListReq, opts ...grpc.CallOption) (*NewsHotListRes, error) {
	out := new(NewsHotListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsHotList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsCollectList(ctx context.Context, in *NewsCollectReq, opts ...grpc.CallOption) (*NewsCollectRes, error) {
	out := new(NewsCollectRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsCollectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsCollectStatusCheck(ctx context.Context, in *NewsCollectStatusCheckReq, opts ...grpc.CallOption) (*NewsCollectStatusCheckRes, error) {
	out := new(NewsCollectStatusCheckRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsCollectStatusCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsCollectOp(ctx context.Context, in *NewsCollectOpReq, opts ...grpc.CallOption) (*NewsCollectOpRes, error) {
	out := new(NewsCollectOpRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsCollectOp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsShareOp(ctx context.Context, in *NewsShareOpReq, opts ...grpc.CallOption) (*NewsShareOpRes, error) {
	out := new(NewsShareOpRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsShareOp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsViewOp(ctx context.Context, in *NewsViewOpReq, opts ...grpc.CallOption) (*NewsViewOpRes, error) {
	out := new(NewsViewOpRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsViewOp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) UserBookmark(ctx context.Context, in *UserBookmarkReq, opts ...grpc.CallOption) (*UserBookmarkRes, error) {
	out := new(UserBookmarkRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/UserBookmark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NewsServiceServer is the server API for NewsService service.
// All implementations must embed UnimplementedNewsServiceServer
// for forward compatibility
type NewsServiceServer interface {
	// 新闻分类列表-done
	NewsCategoryList(context.Context, *NewsCategoryListReq) (*NewsCategoryListRes, error)
	// 新闻列表通过分类ID-done
	NewsListByCateId(context.Context, *NewsListByCateIdReq) (*NewsListByCateIdRes, error)
	// 新闻专题列表-done
	NewsTopicList(context.Context, *NewsTopicListReq) (*NewsTopicListRes, error)
	// 新闻列表通过专题ID
	NewsListByTopicId(context.Context, *NewsListByTopicIdReq) (*NewsListByTopicIdRes, error)
	// 新闻详情
	NewsInfo(context.Context, *NewsInfoReq) (*NewsInfoRes, error)
	// 热门新闻列表
	NewsHotList(context.Context, *NewsHotListReq) (*NewsHotListRes, error)
	// 新闻收藏列表
	NewsCollectList(context.Context, *NewsCollectReq) (*NewsCollectRes, error)
	// 新闻收藏状态检查
	NewsCollectStatusCheck(context.Context, *NewsCollectStatusCheckReq) (*NewsCollectStatusCheckRes, error)
	// 新闻收藏操作
	NewsCollectOp(context.Context, *NewsCollectOpReq) (*NewsCollectOpRes, error)
	// 新闻分享操作
	NewsShareOp(context.Context, *NewsShareOpReq) (*NewsShareOpRes, error)
	// 新闻浏览操作
	NewsViewOp(context.Context, *NewsViewOpReq) (*NewsViewOpRes, error)
	// 用户书签信息
	UserBookmark(context.Context, *UserBookmarkReq) (*UserBookmarkRes, error)
	mustEmbedUnimplementedNewsServiceServer()
}

// UnimplementedNewsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNewsServiceServer struct {
}

func (UnimplementedNewsServiceServer) NewsCategoryList(context.Context, *NewsCategoryListReq) (*NewsCategoryListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsCategoryList not implemented")
}
func (UnimplementedNewsServiceServer) NewsListByCateId(context.Context, *NewsListByCateIdReq) (*NewsListByCateIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsListByCateId not implemented")
}
func (UnimplementedNewsServiceServer) NewsTopicList(context.Context, *NewsTopicListReq) (*NewsTopicListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsTopicList not implemented")
}
func (UnimplementedNewsServiceServer) NewsListByTopicId(context.Context, *NewsListByTopicIdReq) (*NewsListByTopicIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsListByTopicId not implemented")
}
func (UnimplementedNewsServiceServer) NewsInfo(context.Context, *NewsInfoReq) (*NewsInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsInfo not implemented")
}
func (UnimplementedNewsServiceServer) NewsHotList(context.Context, *NewsHotListReq) (*NewsHotListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsHotList not implemented")
}
func (UnimplementedNewsServiceServer) NewsCollectList(context.Context, *NewsCollectReq) (*NewsCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsCollectList not implemented")
}
func (UnimplementedNewsServiceServer) NewsCollectStatusCheck(context.Context, *NewsCollectStatusCheckReq) (*NewsCollectStatusCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsCollectStatusCheck not implemented")
}
func (UnimplementedNewsServiceServer) NewsCollectOp(context.Context, *NewsCollectOpReq) (*NewsCollectOpRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsCollectOp not implemented")
}
func (UnimplementedNewsServiceServer) NewsShareOp(context.Context, *NewsShareOpReq) (*NewsShareOpRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsShareOp not implemented")
}
func (UnimplementedNewsServiceServer) NewsViewOp(context.Context, *NewsViewOpReq) (*NewsViewOpRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsViewOp not implemented")
}
func (UnimplementedNewsServiceServer) UserBookmark(context.Context, *UserBookmarkReq) (*UserBookmarkRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBookmark not implemented")
}
func (UnimplementedNewsServiceServer) mustEmbedUnimplementedNewsServiceServer() {}

// UnsafeNewsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NewsServiceServer will
// result in compilation errors.
type UnsafeNewsServiceServer interface {
	mustEmbedUnimplementedNewsServiceServer()
}

func RegisterNewsServiceServer(s *grpc.Server, srv NewsServiceServer) {
	s.RegisterService(&_NewsService_serviceDesc, srv)
}

func _NewsService_NewsCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsCategoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsCategoryList(ctx, req.(*NewsCategoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsListByCateId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsListByCateIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsListByCateId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsListByCateId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsListByCateId(ctx, req.(*NewsListByCateIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsTopicList(ctx, req.(*NewsTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsListByTopicId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsListByTopicIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsListByTopicId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsListByTopicId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsListByTopicId(ctx, req.(*NewsListByTopicIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsInfo(ctx, req.(*NewsInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsHotList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsHotListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsHotList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsHotList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsHotList(ctx, req.(*NewsHotListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsCollectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsCollectList(ctx, req.(*NewsCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsCollectStatusCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsCollectStatusCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsCollectStatusCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsCollectStatusCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsCollectStatusCheck(ctx, req.(*NewsCollectStatusCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsCollectOp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsCollectOpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsCollectOp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsCollectOp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsCollectOp(ctx, req.(*NewsCollectOpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsShareOp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsShareOpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsShareOp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsShareOp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsShareOp(ctx, req.(*NewsShareOpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsViewOp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsViewOpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsViewOp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsViewOp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsViewOp(ctx, req.(*NewsViewOpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_UserBookmark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBookmarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).UserBookmark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/UserBookmark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).UserBookmark(ctx, req.(*UserBookmarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NewsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.NewsService",
	HandlerType: (*NewsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewsCategoryList",
			Handler:    _NewsService_NewsCategoryList_Handler,
		},
		{
			MethodName: "NewsListByCateId",
			Handler:    _NewsService_NewsListByCateId_Handler,
		},
		{
			MethodName: "NewsTopicList",
			Handler:    _NewsService_NewsTopicList_Handler,
		},
		{
			MethodName: "NewsListByTopicId",
			Handler:    _NewsService_NewsListByTopicId_Handler,
		},
		{
			MethodName: "NewsInfo",
			Handler:    _NewsService_NewsInfo_Handler,
		},
		{
			MethodName: "NewsHotList",
			Handler:    _NewsService_NewsHotList_Handler,
		},
		{
			MethodName: "NewsCollectList",
			Handler:    _NewsService_NewsCollectList_Handler,
		},
		{
			MethodName: "NewsCollectStatusCheck",
			Handler:    _NewsService_NewsCollectStatusCheck_Handler,
		},
		{
			MethodName: "NewsCollectOp",
			Handler:    _NewsService_NewsCollectOp_Handler,
		},
		{
			MethodName: "NewsShareOp",
			Handler:    _NewsService_NewsShareOp_Handler,
		},
		{
			MethodName: "NewsViewOp",
			Handler:    _NewsService_NewsViewOp_Handler,
		},
		{
			MethodName: "UserBookmark",
			Handler:    _NewsService_UserBookmark_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/news.proto",
}
