// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CheckUpdateServiceClient is the client API for CheckUpdateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckUpdateServiceClient interface {
	// app 检查更新
	CheckUpdate(ctx context.Context, in *CheckUpdateReq, opts ...grpc.CallOption) (*CheckUpdateRes, error)
}

type checkUpdateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckUpdateServiceClient(cc grpc.ClientConnInterface) CheckUpdateServiceClient {
	return &checkUpdateServiceClient{cc}
}

func (c *checkUpdateServiceClient) CheckUpdate(ctx context.Context, in *CheckUpdateReq, opts ...grpc.CallOption) (*CheckUpdateRes, error) {
	out := new(CheckUpdateRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.CheckUpdateService/CheckUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckUpdateServiceServer is the server API for CheckUpdateService service.
// All implementations must embed UnimplementedCheckUpdateServiceServer
// for forward compatibility
type CheckUpdateServiceServer interface {
	// app 检查更新
	CheckUpdate(context.Context, *CheckUpdateReq) (*CheckUpdateRes, error)
	mustEmbedUnimplementedCheckUpdateServiceServer()
}

// UnimplementedCheckUpdateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCheckUpdateServiceServer struct {
}

func (UnimplementedCheckUpdateServiceServer) CheckUpdate(context.Context, *CheckUpdateReq) (*CheckUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpdate not implemented")
}
func (UnimplementedCheckUpdateServiceServer) mustEmbedUnimplementedCheckUpdateServiceServer() {}

// UnsafeCheckUpdateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckUpdateServiceServer will
// result in compilation errors.
type UnsafeCheckUpdateServiceServer interface {
	mustEmbedUnimplementedCheckUpdateServiceServer()
}

func RegisterCheckUpdateServiceServer(s *grpc.Server, srv CheckUpdateServiceServer) {
	s.RegisterService(&_CheckUpdateService_serviceDesc, srv)
}

func _CheckUpdateService_CheckUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckUpdateServiceServer).CheckUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.CheckUpdateService/CheckUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckUpdateServiceServer).CheckUpdate(ctx, req.(*CheckUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CheckUpdateService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.CheckUpdateService",
	HandlerType: (*CheckUpdateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUpdate",
			Handler:    _CheckUpdateService_CheckUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/check_update.proto",
}
