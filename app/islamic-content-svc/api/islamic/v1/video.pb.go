// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/video.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// playlist列表信息
type PlaylistItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlaylistId  uint32 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"`       // 播放列表ID
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                                // 播放列表名称（当前语言）
	ShortTitle  string `protobuf:"bytes,3,opt,name=short_title,json=shortTitle,proto3" json:"short_title,omitempty" dc:"播放列表短标题（当前语言）"` // 播放列表短标题（当前语言）
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"播放列表描述（当前语言）"`                  // 播放列表描述（当前语言）
	CoverUrl    string `protobuf:"bytes,5,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty" dc:"专题封面图片链接"`            // 专题封面图片链接
	VideoCount  uint32 `protobuf:"varint,6,opt,name=video_count,json=videoCount,proto3" json:"video_count,omitempty" dc:"播放列表下视频数量"`    // 播放列表下视频数量
}

func (x *PlaylistItem) Reset() {
	*x = PlaylistItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaylistItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistItem) ProtoMessage() {}

func (x *PlaylistItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistItem.ProtoReflect.Descriptor instead.
func (*PlaylistItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{0}
}

func (x *PlaylistItem) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlaylistItem) GetShortTitle() string {
	if x != nil {
		return x.ShortTitle
	}
	return ""
}

func (x *PlaylistItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PlaylistItem) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *PlaylistItem) GetVideoCount() uint32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

// playlist列表请求
type PlaylistListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32              `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Page       *common.PageRequest `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                // 分页参数
}

func (x *PlaylistListReq) Reset() {
	*x = PlaylistListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaylistListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListReq) ProtoMessage() {}

func (x *PlaylistListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListReq.ProtoReflect.Descriptor instead.
func (*PlaylistListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{1}
}

func (x *PlaylistListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *PlaylistListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// playlist列表响应数据
type PlaylistListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*PlaylistItem      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"播放列表列表"` // 播放列表列表
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
}

func (x *PlaylistListResData) Reset() {
	*x = PlaylistListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaylistListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListResData) ProtoMessage() {}

func (x *PlaylistListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListResData.ProtoReflect.Descriptor instead.
func (*PlaylistListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{2}
}

func (x *PlaylistListResData) GetList() []*PlaylistItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PlaylistListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// playlist列表响应
type PlaylistListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error        `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *PlaylistListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PlaylistListRes) Reset() {
	*x = PlaylistListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaylistListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListRes) ProtoMessage() {}

func (x *PlaylistListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListRes.ProtoReflect.Descriptor instead.
func (*PlaylistListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{3}
}

func (x *PlaylistListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlaylistListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PlaylistListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PlaylistListRes) GetData() *PlaylistListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频列表项（用于列表展示的简化版本）
type VideoListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId       uint32 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                         // 视频ID
	CategoryId    uint32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                // 分类ID
	Title         string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                       // 视频标题（当前语言）
	VideoCoverUrl string `protobuf:"bytes,4,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"` // 视频封面图片URL
	VideoDuration uint32 `protobuf:"varint,5,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty" dc:"视频时长(秒)"`    // 视频时长(秒)
	CategoryName  string `protobuf:"bytes,6,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"分类名称（当前语言）"`     // 分类名称（当前语言）
}

func (x *VideoListItem) Reset() {
	*x = VideoListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListItem) ProtoMessage() {}

func (x *VideoListItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListItem.ProtoReflect.Descriptor instead.
func (*VideoListItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{4}
}

func (x *VideoListItem) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoListItem) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *VideoListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VideoListItem) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *VideoListItem) GetVideoDuration() uint32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *VideoListItem) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

// 视频详情信息（完整版本）
type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId          uint32 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                                            // 视频ID
	CategoryId       uint32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                                   // 分类ID
	Title            string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                                          // 视频标题（当前语言）
	Description      string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"视频描述（当前语言）"`                                              // 视频描述（当前语言）
	VideoCoverUrl    string `protobuf:"bytes,5,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"`                    // 视频封面图片URL
	VideoUrl         string `protobuf:"bytes,6,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty" dc:"视频文件URL"`                                       // 视频文件URL
	Author           string `protobuf:"bytes,7,opt,name=author,proto3" json:"author,omitempty" dc:"视频作者"`                                                              // 视频作者
	AuthorLogo       string `protobuf:"bytes,8,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"作者头像URL"`                                 // 作者头像URL
	AuthorAuthStatus uint32 `protobuf:"varint,9,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"作者认证状态：0-未认证，1-已认证"` // 作者认证状态：0-未认证，1-已认证
	PublishState     uint32 `protobuf:"varint,10,opt,name=publish_state,json=publishState,proto3" json:"publish_state,omitempty" dc:"发布状态：0-待发布，1-已发布，2-已下线"`          // 发布状态：0-待发布，1-已发布，2-已下线
	IsCollected      bool   `protobuf:"varint,11,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"当前用户是否已收藏（需要登录）"`                    // 当前用户是否已收藏（需要登录）
	CategoryName     string `protobuf:"bytes,12,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"分类名称（当前语言）"`                       // 分类名称（当前语言）
	PublishTime      int64  `protobuf:"varint,13,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间 (时间戳, 毫秒)"`                     // 发布时间 (时间戳, 毫秒)
}

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{5}
}

func (x *Video) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *Video) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Video) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Video) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Video) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *Video) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Video) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *Video) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

func (x *Video) GetPublishState() uint32 {
	if x != nil {
		return x.PublishState
	}
	return 0
}

func (x *Video) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

func (x *Video) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *Video) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

// 视频列表请求（支持多种查询方式）
type VideoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32              `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"`                         // 语言ID
	CategoryId uint32              `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（可选）"`                     // 分类ID（可选）
	PlaylistId uint32              `protobuf:"varint,3,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID（可选）"`                   // 播放列表ID（可选）
	Title      string              `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题搜索（可选）"`                                                // 视频标题搜索（可选）
	SortBy     string              `protobuf:"bytes,5,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty" dc:"排序方式：view_count, created_at, published_at"` // 排序方式：view_count, created_at, published_at
	SortOrder  string              `protobuf:"bytes,6,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty" dc:"排序顺序：asc, desc"`                   // 排序顺序：asc, desc
	Page       *common.PageRequest `protobuf:"bytes,7,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                        // 分页参数
}

func (x *VideoListReq) Reset() {
	*x = VideoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListReq) ProtoMessage() {}

func (x *VideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListReq.ProtoReflect.Descriptor instead.
func (*VideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{6}
}

func (x *VideoListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoListReq) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *VideoListReq) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *VideoListReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VideoListReq) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *VideoListReq) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *VideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频列表响应数据
type VideoListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*VideoListItem     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"视频列表"`                                  // 视频列表
	Page     *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`                                  // 分页信息
	Playlist *PlaylistBasicInfo   `protobuf:"bytes,3,opt,name=playlist,proto3" json:"playlist,omitempty" dc:"播放列表基本信息（当通过playlist_id查询时返回）"` // 播放列表基本信息（当通过playlist_id查询时返回）
}

func (x *VideoListResData) Reset() {
	*x = VideoListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListResData) ProtoMessage() {}

func (x *VideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListResData.ProtoReflect.Descriptor instead.
func (*VideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{7}
}

func (x *VideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *VideoListResData) GetPlaylist() *PlaylistBasicInfo {
	if x != nil {
		return x.Playlist
	}
	return nil
}

// 视频列表响应
type VideoListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *VideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VideoListRes) Reset() {
	*x = VideoListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListRes) ProtoMessage() {}

func (x *VideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListRes.ProtoReflect.Descriptor instead.
func (*VideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{8}
}

func (x *VideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoListRes) GetData() *VideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频详情请求
type VideoDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	VideoId    uint32 `protobuf:"varint,2,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`          // 视频ID
}

func (x *VideoDetailReq) Reset() {
	*x = VideoDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailReq) ProtoMessage() {}

func (x *VideoDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailReq.ProtoReflect.Descriptor instead.
func (*VideoDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{9}
}

func (x *VideoDetailReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoDetailReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 视频详情响应
type VideoDetailRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *Video        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VideoDetailRes) Reset() {
	*x = VideoDetailRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailRes) ProtoMessage() {}

func (x *VideoDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailRes.ProtoReflect.Descriptor instead.
func (*VideoDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{10}
}

func (x *VideoDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoDetailRes) GetData() *Video {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频收藏请求
type VideoCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId uint32 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`         // 视频ID
	IsAdd   uint32 `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` // 是否添加收藏，1-添加，0-取消收藏
}

func (x *VideoCollectReq) Reset() {
	*x = VideoCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectReq) ProtoMessage() {}

func (x *VideoCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectReq.ProtoReflect.Descriptor instead.
func (*VideoCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{11}
}

func (x *VideoCollectReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

// 视频收藏响应
type VideoCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *VideoCollectRes) Reset() {
	*x = VideoCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectRes) ProtoMessage() {}

func (x *VideoCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectRes.ProtoReflect.Descriptor instead.
func (*VideoCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{12}
}

func (x *VideoCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 视频收藏列表请求
type VideoCollectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32              `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Page       *common.PageRequest `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                // 分页参数
}

func (x *VideoCollectListReq) Reset() {
	*x = VideoCollectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListReq) ProtoMessage() {}

func (x *VideoCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListReq.ProtoReflect.Descriptor instead.
func (*VideoCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{13}
}

func (x *VideoCollectListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应数据
type VideoCollectListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*VideoListItem     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"收藏的视频列表"` // 收藏的视频列表
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`    // 分页信息
}

func (x *VideoCollectListResData) Reset() {
	*x = VideoCollectListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListResData) ProtoMessage() {}

func (x *VideoCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListResData.ProtoReflect.Descriptor instead.
func (*VideoCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{14}
}

func (x *VideoCollectListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应
type VideoCollectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *VideoCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VideoCollectListRes) Reset() {
	*x = VideoCollectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListRes) ProtoMessage() {}

func (x *VideoCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListRes.ProtoReflect.Descriptor instead.
func (*VideoCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{15}
}

func (x *VideoCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoCollectListRes) GetData() *VideoCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 播放列表基本信息（用于VideoList返回）
type PlaylistBasicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlaylistId uint32 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"` // 播放列表ID
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                          // 播放列表名称（当前语言）
	CoverUrl   string `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty" dc:"播放列表封面图片URL"`   // 播放列表封面图片URL
}

func (x *PlaylistBasicInfo) Reset() {
	*x = PlaylistBasicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaylistBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistBasicInfo) ProtoMessage() {}

func (x *PlaylistBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistBasicInfo.ProtoReflect.Descriptor instead.
func (*PlaylistBasicInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{16}
}

func (x *PlaylistBasicInfo) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistBasicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlaylistBasicInfo) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

// 推荐视频列表请求
type RecommendedVideoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32              `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"`     // 语言ID
	CategoryId uint32              `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（可选）"` // 分类ID（可选）
	Page       *common.PageRequest `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
}

func (x *RecommendedVideoListReq) Reset() {
	*x = RecommendedVideoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendedVideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListReq) ProtoMessage() {}

func (x *RecommendedVideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListReq.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{17}
}

func (x *RecommendedVideoListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *RecommendedVideoListReq) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *RecommendedVideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应数据
type RecommendedVideoListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*VideoListItem     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"推荐视频列表"` // 推荐视频列表
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
}

func (x *RecommendedVideoListResData) Reset() {
	*x = RecommendedVideoListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendedVideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListResData) ProtoMessage() {}

func (x *RecommendedVideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListResData.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{18}
}

func (x *RecommendedVideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecommendedVideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应
type RecommendedVideoListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *RecommendedVideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *RecommendedVideoListRes) Reset() {
	*x = RecommendedVideoListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendedVideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListRes) ProtoMessage() {}

func (x *RecommendedVideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListRes.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{19}
}

func (x *RecommendedVideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecommendedVideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RecommendedVideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RecommendedVideoListRes) GetData() *RecommendedVideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频收藏状态检查请求
type VideoCollectStatusCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId uint32 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
}

func (x *VideoCollectStatusCheckReq) Reset() {
	*x = VideoCollectStatusCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectStatusCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckReq) ProtoMessage() {}

func (x *VideoCollectStatusCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckReq.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{20}
}

func (x *VideoCollectStatusCheckReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

type VideoCollectStatusCheckData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCollect int32 `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty" 1:"没收藏, 2: 已收藏"` // 1: 没收藏, 2: 已收藏
}

func (x *VideoCollectStatusCheckData) Reset() {
	*x = VideoCollectStatusCheckData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectStatusCheckData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckData) ProtoMessage() {}

func (x *VideoCollectStatusCheckData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckData.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{21}
}

func (x *VideoCollectStatusCheckData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

// 视频收藏状态检查响应
type VideoCollectStatusCheckRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *VideoCollectStatusCheckData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VideoCollectStatusCheckRes) Reset() {
	*x = VideoCollectStatusCheckRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoCollectStatusCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckRes) ProtoMessage() {}

func (x *VideoCollectStatusCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckRes.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{22}
}

func (x *VideoCollectStatusCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectStatusCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectStatusCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoCollectStatusCheckRes) GetData() *VideoCollectStatusCheckData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频分享请求
type VideoShareReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId uint32 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
}

func (x *VideoShareReq) Reset() {
	*x = VideoShareReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoShareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoShareReq) ProtoMessage() {}

func (x *VideoShareReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoShareReq.ProtoReflect.Descriptor instead.
func (*VideoShareReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{23}
}

func (x *VideoShareReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 视频分享响应数据 (先返回空数据)
type VideoShareData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VideoShareData) Reset() {
	*x = VideoShareData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoShareData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoShareData) ProtoMessage() {}

func (x *VideoShareData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoShareData.ProtoReflect.Descriptor instead.
func (*VideoShareData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{24}
}

// 视频分享响应
type VideoShareRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *VideoShareData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VideoShareRes) Reset() {
	*x = VideoShareRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_video_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoShareRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoShareRes) ProtoMessage() {}

func (x *VideoShareRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoShareRes.ProtoReflect.Descriptor instead.
func (*VideoShareRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{25}
}

func (x *VideoShareRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoShareRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoShareRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoShareRes) GetData() *VideoShareData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_video_proto protoreflect.FileDescriptor

var file_islamic_v1_video_proto_rawDesc = []byte{
	0x0a, 0x16, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x01, 0x0a, 0x0c, 0x50, 0x6c,
	0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c,
	0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12,
	0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x5b, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x6d, 0x0a,
	0x13, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x91, 0x01, 0x0a,
	0x0f, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x33, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xd5, 0x01, 0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb7, 0x03, 0x0a, 0x05, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x6c, 0x6f,
	0x67, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79,
	0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x6f, 0x72, 0x74, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xa6, 0x01,
	0x0a, 0x10, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x6c,
	0x69, 0x73, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x49, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x43, 0x0a, 0x0f, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x73, 0x41, 0x64, 0x64, 0x22, 0x5c, 0x0a, 0x0f,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x5f, 0x0a, 0x13, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x17, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22,
	0x99, 0x01, 0x0a, 0x13, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x65, 0x0a, 0x11, 0x50,
	0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55,
	0x72, 0x6c, 0x22, 0x84, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x76, 0x0a, 0x1b, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x22, 0xa1, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x37, 0x0a, 0x1a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x22, 0x3c,
	0x0a, 0x1b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x22, 0xa4, 0x01, 0x0a,
	0x1a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x22,
	0x10, 0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x8a, 0x01, 0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x91,
	0x05, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x48, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69,
	0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x12, 0x60, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x23,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x12, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x54, 0x0a,
	0x10, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x17, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x26,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x19, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_video_proto_rawDescOnce sync.Once
	file_islamic_v1_video_proto_rawDescData = file_islamic_v1_video_proto_rawDesc
)

func file_islamic_v1_video_proto_rawDescGZIP() []byte {
	file_islamic_v1_video_proto_rawDescOnce.Do(func() {
		file_islamic_v1_video_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_video_proto_rawDescData)
	})
	return file_islamic_v1_video_proto_rawDescData
}

var file_islamic_v1_video_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_islamic_v1_video_proto_goTypes = []interface{}{
	(*PlaylistItem)(nil),                // 0: islamic.v1.PlaylistItem
	(*PlaylistListReq)(nil),             // 1: islamic.v1.PlaylistListReq
	(*PlaylistListResData)(nil),         // 2: islamic.v1.PlaylistListResData
	(*PlaylistListRes)(nil),             // 3: islamic.v1.PlaylistListRes
	(*VideoListItem)(nil),               // 4: islamic.v1.VideoListItem
	(*Video)(nil),                       // 5: islamic.v1.Video
	(*VideoListReq)(nil),                // 6: islamic.v1.VideoListReq
	(*VideoListResData)(nil),            // 7: islamic.v1.VideoListResData
	(*VideoListRes)(nil),                // 8: islamic.v1.VideoListRes
	(*VideoDetailReq)(nil),              // 9: islamic.v1.VideoDetailReq
	(*VideoDetailRes)(nil),              // 10: islamic.v1.VideoDetailRes
	(*VideoCollectReq)(nil),             // 11: islamic.v1.VideoCollectReq
	(*VideoCollectRes)(nil),             // 12: islamic.v1.VideoCollectRes
	(*VideoCollectListReq)(nil),         // 13: islamic.v1.VideoCollectListReq
	(*VideoCollectListResData)(nil),     // 14: islamic.v1.VideoCollectListResData
	(*VideoCollectListRes)(nil),         // 15: islamic.v1.VideoCollectListRes
	(*PlaylistBasicInfo)(nil),           // 16: islamic.v1.PlaylistBasicInfo
	(*RecommendedVideoListReq)(nil),     // 17: islamic.v1.RecommendedVideoListReq
	(*RecommendedVideoListResData)(nil), // 18: islamic.v1.RecommendedVideoListResData
	(*RecommendedVideoListRes)(nil),     // 19: islamic.v1.RecommendedVideoListRes
	(*VideoCollectStatusCheckReq)(nil),  // 20: islamic.v1.VideoCollectStatusCheckReq
	(*VideoCollectStatusCheckData)(nil), // 21: islamic.v1.VideoCollectStatusCheckData
	(*VideoCollectStatusCheckRes)(nil),  // 22: islamic.v1.VideoCollectStatusCheckRes
	(*VideoShareReq)(nil),               // 23: islamic.v1.VideoShareReq
	(*VideoShareData)(nil),              // 24: islamic.v1.VideoShareData
	(*VideoShareRes)(nil),               // 25: islamic.v1.VideoShareRes
	(*common.PageRequest)(nil),          // 26: common.PageRequest
	(*common.PageResponse)(nil),         // 27: common.PageResponse
	(*common.Error)(nil),                // 28: common.Error
}
var file_islamic_v1_video_proto_depIdxs = []int32{
	26, // 0: islamic.v1.PlaylistListReq.page:type_name -> common.PageRequest
	0,  // 1: islamic.v1.PlaylistListResData.list:type_name -> islamic.v1.PlaylistItem
	27, // 2: islamic.v1.PlaylistListResData.page:type_name -> common.PageResponse
	28, // 3: islamic.v1.PlaylistListRes.error:type_name -> common.Error
	2,  // 4: islamic.v1.PlaylistListRes.data:type_name -> islamic.v1.PlaylistListResData
	26, // 5: islamic.v1.VideoListReq.page:type_name -> common.PageRequest
	4,  // 6: islamic.v1.VideoListResData.list:type_name -> islamic.v1.VideoListItem
	27, // 7: islamic.v1.VideoListResData.page:type_name -> common.PageResponse
	16, // 8: islamic.v1.VideoListResData.playlist:type_name -> islamic.v1.PlaylistBasicInfo
	28, // 9: islamic.v1.VideoListRes.error:type_name -> common.Error
	7,  // 10: islamic.v1.VideoListRes.data:type_name -> islamic.v1.VideoListResData
	28, // 11: islamic.v1.VideoDetailRes.error:type_name -> common.Error
	5,  // 12: islamic.v1.VideoDetailRes.data:type_name -> islamic.v1.Video
	28, // 13: islamic.v1.VideoCollectRes.error:type_name -> common.Error
	26, // 14: islamic.v1.VideoCollectListReq.page:type_name -> common.PageRequest
	4,  // 15: islamic.v1.VideoCollectListResData.list:type_name -> islamic.v1.VideoListItem
	27, // 16: islamic.v1.VideoCollectListResData.page:type_name -> common.PageResponse
	28, // 17: islamic.v1.VideoCollectListRes.error:type_name -> common.Error
	14, // 18: islamic.v1.VideoCollectListRes.data:type_name -> islamic.v1.VideoCollectListResData
	26, // 19: islamic.v1.RecommendedVideoListReq.page:type_name -> common.PageRequest
	4,  // 20: islamic.v1.RecommendedVideoListResData.list:type_name -> islamic.v1.VideoListItem
	27, // 21: islamic.v1.RecommendedVideoListResData.page:type_name -> common.PageResponse
	28, // 22: islamic.v1.RecommendedVideoListRes.error:type_name -> common.Error
	18, // 23: islamic.v1.RecommendedVideoListRes.data:type_name -> islamic.v1.RecommendedVideoListResData
	28, // 24: islamic.v1.VideoCollectStatusCheckRes.error:type_name -> common.Error
	21, // 25: islamic.v1.VideoCollectStatusCheckRes.data:type_name -> islamic.v1.VideoCollectStatusCheckData
	28, // 26: islamic.v1.VideoShareRes.error:type_name -> common.Error
	24, // 27: islamic.v1.VideoShareRes.data:type_name -> islamic.v1.VideoShareData
	1,  // 28: islamic.v1.VideoService.PlaylistList:input_type -> islamic.v1.PlaylistListReq
	6,  // 29: islamic.v1.VideoService.VideoList:input_type -> islamic.v1.VideoListReq
	9,  // 30: islamic.v1.VideoService.VideoDetail:input_type -> islamic.v1.VideoDetailReq
	17, // 31: islamic.v1.VideoService.RecommendedVideoList:input_type -> islamic.v1.RecommendedVideoListReq
	11, // 32: islamic.v1.VideoService.VideoCollect:input_type -> islamic.v1.VideoCollectReq
	13, // 33: islamic.v1.VideoService.VideoCollectList:input_type -> islamic.v1.VideoCollectListReq
	20, // 34: islamic.v1.VideoService.VideoCollectStatusCheck:input_type -> islamic.v1.VideoCollectStatusCheckReq
	23, // 35: islamic.v1.VideoService.VideoShare:input_type -> islamic.v1.VideoShareReq
	3,  // 36: islamic.v1.VideoService.PlaylistList:output_type -> islamic.v1.PlaylistListRes
	8,  // 37: islamic.v1.VideoService.VideoList:output_type -> islamic.v1.VideoListRes
	10, // 38: islamic.v1.VideoService.VideoDetail:output_type -> islamic.v1.VideoDetailRes
	19, // 39: islamic.v1.VideoService.RecommendedVideoList:output_type -> islamic.v1.RecommendedVideoListRes
	12, // 40: islamic.v1.VideoService.VideoCollect:output_type -> islamic.v1.VideoCollectRes
	15, // 41: islamic.v1.VideoService.VideoCollectList:output_type -> islamic.v1.VideoCollectListRes
	22, // 42: islamic.v1.VideoService.VideoCollectStatusCheck:output_type -> islamic.v1.VideoCollectStatusCheckRes
	25, // 43: islamic.v1.VideoService.VideoShare:output_type -> islamic.v1.VideoShareRes
	36, // [36:44] is the sub-list for method output_type
	28, // [28:36] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_islamic_v1_video_proto_init() }
func file_islamic_v1_video_proto_init() {
	if File_islamic_v1_video_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_video_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaylistItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaylistListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaylistListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaylistListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoDetailRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaylistBasicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendedVideoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendedVideoListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendedVideoListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectStatusCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectStatusCheckData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoCollectStatusCheckRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoShareReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoShareData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_video_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoShareRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_video_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_video_proto_goTypes,
		DependencyIndexes: file_islamic_v1_video_proto_depIdxs,
		MessageInfos:      file_islamic_v1_video_proto_msgTypes,
	}.Build()
	File_islamic_v1_video_proto = out.File
	file_islamic_v1_video_proto_rawDesc = nil
	file_islamic_v1_video_proto_goTypes = nil
	file_islamic_v1_video_proto_depIdxs = nil
}
