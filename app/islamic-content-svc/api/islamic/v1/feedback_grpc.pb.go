// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// FeedbackServiceClient is the client API for FeedbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FeedbackServiceClient interface {
	FeedbackAdd(ctx context.Context, in *FeedbackAddReq, opts ...grpc.CallOption) (*FeedbackAddRes, error)
}

type feedbackServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFeedbackServiceClient(cc grpc.ClientConnInterface) FeedbackServiceClient {
	return &feedbackServiceClient{cc}
}

func (c *feedbackServiceClient) FeedbackAdd(ctx context.Context, in *FeedbackAddReq, opts ...grpc.CallOption) (*FeedbackAddRes, error) {
	out := new(FeedbackAddRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FeedbackService/FeedbackAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeedbackServiceServer is the server API for FeedbackService service.
// All implementations must embed UnimplementedFeedbackServiceServer
// for forward compatibility
type FeedbackServiceServer interface {
	FeedbackAdd(context.Context, *FeedbackAddReq) (*FeedbackAddRes, error)
	mustEmbedUnimplementedFeedbackServiceServer()
}

// UnimplementedFeedbackServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFeedbackServiceServer struct {
}

func (UnimplementedFeedbackServiceServer) FeedbackAdd(context.Context, *FeedbackAddReq) (*FeedbackAddRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FeedbackAdd not implemented")
}
func (UnimplementedFeedbackServiceServer) mustEmbedUnimplementedFeedbackServiceServer() {}

// UnsafeFeedbackServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FeedbackServiceServer will
// result in compilation errors.
type UnsafeFeedbackServiceServer interface {
	mustEmbedUnimplementedFeedbackServiceServer()
}

func RegisterFeedbackServiceServer(s *grpc.Server, srv FeedbackServiceServer) {
	s.RegisterService(&_FeedbackService_serviceDesc, srv)
}

func _FeedbackService_FeedbackAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FeedbackAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).FeedbackAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FeedbackService/FeedbackAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).FeedbackAdd(ctx, req.(*FeedbackAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FeedbackService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.FeedbackService",
	HandlerType: (*FeedbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FeedbackAdd",
			Handler:    _FeedbackService_FeedbackAdd_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/feedback.proto",
}
