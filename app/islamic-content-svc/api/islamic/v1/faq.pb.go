// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/faq.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FAQ分类列表请求
type FaqCateListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
}

func (x *FaqCateListReq) Reset() {
	*x = FaqCateListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqCateListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateListReq) ProtoMessage() {}

func (x *FaqCateListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateListReq.ProtoReflect.Descriptor instead.
func (*FaqCateListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{0}
}

func (x *FaqCateListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

// FAQ分类列表数据
type FaqCateListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*FaqCateItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *FaqCateListData) Reset() {
	*x = FaqCateListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqCateListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateListData) ProtoMessage() {}

func (x *FaqCateListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateListData.ProtoReflect.Descriptor instead.
func (*FaqCateListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{1}
}

func (x *FaqCateListData) GetList() []*FaqCateItem {
	if x != nil {
		return x.List
	}
	return nil
}

// FAQ分类列表响应
type FaqCateListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *FaqCateListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"分类列表"` // 分类列表
}

func (x *FaqCateListRes) Reset() {
	*x = FaqCateListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqCateListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateListRes) ProtoMessage() {}

func (x *FaqCateListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateListRes.ProtoReflect.Descriptor instead.
func (*FaqCateListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{2}
}

func (x *FaqCateListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqCateListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqCateListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqCateListRes) GetData() *FaqCateListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// FAQ分类列表item
type FaqCateItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"分类ID"`                                      // 分类ID
	IsOpen     int32  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty" dc:"是否启用"`                // 是否启用
	Sort       int32  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty" dc:"排序"`                                    // 排序
	CateCount  int32  `protobuf:"varint,4,opt,name=cate_count,json=cateCount,proto3" json:"cate_count,omitempty" dc:"该分类下的FAQ数量"` // 该分类下的FAQ数量
	Title      string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty" dc:"分类名称"`                                 // 分类名称
	LanguageId uint32 `protobuf:"varint,6,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`    // 语言id
}

func (x *FaqCateItem) Reset() {
	*x = FaqCateItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqCateItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateItem) ProtoMessage() {}

func (x *FaqCateItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateItem.ProtoReflect.Descriptor instead.
func (*FaqCateItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{3}
}

func (x *FaqCateItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqCateItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqCateItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqCateItem) GetCateCount() int32 {
	if x != nil {
		return x.CateCount
	}
	return 0
}

func (x *FaqCateItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqCateItem) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

// FAQ列表by分类ID请求
type FaqListByCateIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32              `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`                            // 语言id
	CateId     uint32              `protobuf:"varint,2,opt,name=cate_id,json=cateId,proto3" json:"cate_id,omitempty" dc:"分类id"`                                        // 分类id
	Keyword    string              `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty" dc:"搜索关键字"`                                                    // 搜索关键字
	Page       *common.PageRequest `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                           // 分页参数
	PositionId uint32              `protobuf:"varint,5,opt,name=position_id,json=positionId,proto3" json:"position_id,omitempty" dc:"位置id 1 我的(关于) 2 信仰(朝觐) 3 信仰(副朝)"` // 位置id 1 我的(关于) 2 信仰(朝觐) 3 信仰(副朝)
}

func (x *FaqListByCateIdReq) Reset() {
	*x = FaqListByCateIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqListByCateIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListByCateIdReq) ProtoMessage() {}

func (x *FaqListByCateIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListByCateIdReq.ProtoReflect.Descriptor instead.
func (*FaqListByCateIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{4}
}

func (x *FaqListByCateIdReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqListByCateIdReq) GetCateId() uint32 {
	if x != nil {
		return x.CateId
	}
	return 0
}

func (x *FaqListByCateIdReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *FaqListByCateIdReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *FaqListByCateIdReq) GetPositionId() uint32 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

type FaqListByCateIdData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*FaqQuestionItem   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *FaqListByCateIdData) Reset() {
	*x = FaqListByCateIdData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqListByCateIdData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListByCateIdData) ProtoMessage() {}

func (x *FaqListByCateIdData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListByCateIdData.ProtoReflect.Descriptor instead.
func (*FaqListByCateIdData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{5}
}

func (x *FaqListByCateIdData) GetList() []*FaqQuestionItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *FaqListByCateIdData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// FAQ列表by分类ID响应
type FaqListByCateIdRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error        `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *FaqListByCateIdData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"FAQ列表"` // FAQ列表
}

func (x *FaqListByCateIdRes) Reset() {
	*x = FaqListByCateIdRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqListByCateIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListByCateIdRes) ProtoMessage() {}

func (x *FaqListByCateIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListByCateIdRes.ProtoReflect.Descriptor instead.
func (*FaqListByCateIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{6}
}

func (x *FaqListByCateIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqListByCateIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqListByCateIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqListByCateIdRes) GetData() *FaqListByCateIdData {
	if x != nil {
		return x.Data
	}
	return nil
}

// FAQ问题item
type FaqQuestionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"问题ID"`                                      // 问题ID
	IsOpen      int32  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty" dc:"是否启用"`                // 是否启用
	Sort        int32  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty" dc:"排序"`                                    // 排序
	Views       int32  `protobuf:"varint,4,opt,name=views,proto3" json:"views,omitempty" dc:"浏览次数"`                                // 浏览次数
	Title       string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty" dc:"问题标题"`                                 // 问题标题
	Desc        string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty" dc:"问题描述"`                                   // 问题描述
	LanguageId  int32  `protobuf:"varint,7,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`    // 语言id
	FaqCateId   uint32 `protobuf:"varint,8,opt,name=faq_cate_id,json=faqCateId,proto3" json:"faq_cate_id,omitempty" dc:"分类id"`     // 分类id
	PublishTime uint64 `protobuf:"varint,9,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间"` //发布时间
}

func (x *FaqQuestionItem) Reset() {
	*x = FaqQuestionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqQuestionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestionItem) ProtoMessage() {}

func (x *FaqQuestionItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestionItem.ProtoReflect.Descriptor instead.
func (*FaqQuestionItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{7}
}

func (x *FaqQuestionItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestionItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqQuestionItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestionItem) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestionItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqQuestionItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FaqQuestionItem) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqQuestionItem) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

func (x *FaqQuestionItem) GetPublishTime() uint64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

// FAQ详情请求
type FaqOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	Id         uint32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" dc:"分类id"`                                   // 分类id
}

func (x *FaqOneReq) Reset() {
	*x = FaqOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqOneReq) ProtoMessage() {}

func (x *FaqOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqOneReq.ProtoReflect.Descriptor instead.
func (*FaqOneReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{8}
}

func (x *FaqOneReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqOneReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// FAQ详情响应
type FaqOneRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *FaqQuestionOneItem `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *FaqOneRes) Reset() {
	*x = FaqOneRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqOneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqOneRes) ProtoMessage() {}

func (x *FaqOneRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqOneRes.ProtoReflect.Descriptor instead.
func (*FaqOneRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{9}
}

func (x *FaqOneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqOneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqOneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqOneRes) GetData() *FaqQuestionOneItem {
	if x != nil {
		return x.Data
	}
	return nil
}

// FAQ问题详情item
type FaqQuestionOneItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"问题ID"`                                          // 问题ID
	IsOpen       int32  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty" dc:"是否启用"`                    // 是否启用
	Sort         int32  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty" dc:"排序"`                                        // 排序
	Views        int32  `protobuf:"varint,4,opt,name=views,proto3" json:"views,omitempty" dc:"浏览次数"`                                    // 浏览次数
	Title        string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty" dc:"问题标题"`                                     // 问题标题
	Desc         string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty" dc:"问题描述"`                                       // 问题描述
	LanguageId   int32  `protobuf:"varint,7,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`        // 语言id
	FaqCateId    uint32 `protobuf:"varint,8,opt,name=faq_cate_id,json=faqCateId,proto3" json:"faq_cate_id,omitempty" dc:"分类id"`         // 分类id
	FaqCateTitle string `protobuf:"bytes,9,opt,name=faq_cate_title,json=faqCateTitle,proto3" json:"faq_cate_title,omitempty" dc:"分类名称"` // 分类名称
	PublishTime  uint64 `protobuf:"varint,10,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间"`    //发布时间
}

func (x *FaqQuestionOneItem) Reset() {
	*x = FaqQuestionOneItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqQuestionOneItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestionOneItem) ProtoMessage() {}

func (x *FaqQuestionOneItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestionOneItem.ProtoReflect.Descriptor instead.
func (*FaqQuestionOneItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{10}
}

func (x *FaqQuestionOneItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestionOneItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqQuestionOneItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestionOneItem) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestionOneItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqQuestionOneItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FaqQuestionOneItem) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqQuestionOneItem) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

func (x *FaqQuestionOneItem) GetFaqCateTitle() string {
	if x != nil {
		return x.FaqCateTitle
	}
	return ""
}

func (x *FaqQuestionOneItem) GetPublishTime() uint64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

type FaqMediaReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FaqMediaReq) Reset() {
	*x = FaqMediaReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqMediaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqMediaReq) ProtoMessage() {}

func (x *FaqMediaReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqMediaReq.ProtoReflect.Descriptor instead.
func (*FaqMediaReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{11}
}

type FaqMediaRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *FaqMediaData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *FaqMediaRes) Reset() {
	*x = FaqMediaRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqMediaRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqMediaRes) ProtoMessage() {}

func (x *FaqMediaRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqMediaRes.ProtoReflect.Descriptor instead.
func (*FaqMediaRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{12}
}

func (x *FaqMediaRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqMediaRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqMediaRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqMediaRes) GetData() *FaqMediaData {
	if x != nil {
		return x.Data
	}
	return nil
}

type FaqMediaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*FaqMediaItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *FaqMediaData) Reset() {
	*x = FaqMediaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqMediaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqMediaData) ProtoMessage() {}

func (x *FaqMediaData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqMediaData.ProtoReflect.Descriptor instead.
func (*FaqMediaData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{13}
}

func (x *FaqMediaData) GetList() []*FaqMediaItem {
	if x != nil {
		return x.List
	}
	return nil
}

type FaqMediaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IconUrl string `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Name    string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	JumpUrl string `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
}

func (x *FaqMediaItem) Reset() {
	*x = FaqMediaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqMediaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqMediaItem) ProtoMessage() {}

func (x *FaqMediaItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqMediaItem.ProtoReflect.Descriptor instead.
func (*FaqMediaItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{14}
}

func (x *FaqMediaItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqMediaItem) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *FaqMediaItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FaqMediaItem) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

type FaqListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                   // 分页参数
	PositionId uint32              `protobuf:"varint,2,opt,name=position_id,json=positionId,proto3" json:"position_id,omitempty" dc:"位置id  2 信仰(朝觐) 3 信仰(副朝)"` // 位置id  2 信仰(朝觐) 3 信仰(副朝)
	Keyword    string              `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty" dc:"搜索关键字"`                                            // 搜索关键字
}

func (x *FaqListReq) Reset() {
	*x = FaqListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_faq_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListReq) ProtoMessage() {}

func (x *FaqListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListReq.ProtoReflect.Descriptor instead.
func (*FaqListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{15}
}

func (x *FaqListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *FaqListReq) GetPositionId() uint32 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

func (x *FaqListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

var File_islamic_v1_faq_proto protoreflect.FileDescriptor

var file_islamic_v1_faq_proto_rawDesc = []byte{
	0x0a, 0x14, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x61, 0x71,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x0e, 0x46, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x0f, 0x46, 0x61, 0x71, 0x43,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x46, 0x61, 0x71,
	0x43, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa0, 0x01, 0x0a, 0x0b, 0x46, 0x61, 0x71, 0x43,
	0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x12, 0x46,
	0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x70, 0x0a, 0x13, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x61, 0x71, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x22, 0x94, 0x01, 0x0a, 0x12, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf2, 0x01, 0x0a, 0x0f, 0x46, 0x61, 0x71,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69,
	0x73, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x66, 0x61,
	0x71, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x66, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3c, 0x0a,
	0x09, 0x46, 0x61, 0x71, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x09,
	0x46, 0x61, 0x71, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x61, 0x71, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9b, 0x02, 0x0a, 0x12, 0x46, 0x61, 0x71,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x66, 0x61, 0x71, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x66, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x66, 0x61, 0x71, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x0d, 0x0a, 0x0b, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x52, 0x65, 0x71, 0x22, 0x86, 0x01, 0x0a, 0x0b, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3c,
	0x0a, 0x0c, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x68, 0x0a, 0x0c,
	0x46, 0x61, 0x71, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6a,
	0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a,
	0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x22, 0x70, 0x0a, 0x0a, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x32, 0xe3, 0x02, 0x0a, 0x0a, 0x46, 0x61, 0x71,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x46, 0x61, 0x71, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x06, 0x46, 0x61, 0x71, 0x4f, 0x6e, 0x65, 0x12,
	0x15, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71,
	0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x12, 0x3c, 0x0a,
	0x08, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52,
	0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x61, 0x71, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x46,
	0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x71, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x42, 0x3c,
	0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f,
	0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_faq_proto_rawDescOnce sync.Once
	file_islamic_v1_faq_proto_rawDescData = file_islamic_v1_faq_proto_rawDesc
)

func file_islamic_v1_faq_proto_rawDescGZIP() []byte {
	file_islamic_v1_faq_proto_rawDescOnce.Do(func() {
		file_islamic_v1_faq_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_faq_proto_rawDescData)
	})
	return file_islamic_v1_faq_proto_rawDescData
}

var file_islamic_v1_faq_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_islamic_v1_faq_proto_goTypes = []interface{}{
	(*FaqCateListReq)(nil),      // 0: islamic.v1.FaqCateListReq
	(*FaqCateListData)(nil),     // 1: islamic.v1.FaqCateListData
	(*FaqCateListRes)(nil),      // 2: islamic.v1.FaqCateListRes
	(*FaqCateItem)(nil),         // 3: islamic.v1.FaqCateItem
	(*FaqListByCateIdReq)(nil),  // 4: islamic.v1.FaqListByCateIdReq
	(*FaqListByCateIdData)(nil), // 5: islamic.v1.FaqListByCateIdData
	(*FaqListByCateIdRes)(nil),  // 6: islamic.v1.FaqListByCateIdRes
	(*FaqQuestionItem)(nil),     // 7: islamic.v1.FaqQuestionItem
	(*FaqOneReq)(nil),           // 8: islamic.v1.FaqOneReq
	(*FaqOneRes)(nil),           // 9: islamic.v1.FaqOneRes
	(*FaqQuestionOneItem)(nil),  // 10: islamic.v1.FaqQuestionOneItem
	(*FaqMediaReq)(nil),         // 11: islamic.v1.FaqMediaReq
	(*FaqMediaRes)(nil),         // 12: islamic.v1.FaqMediaRes
	(*FaqMediaData)(nil),        // 13: islamic.v1.FaqMediaData
	(*FaqMediaItem)(nil),        // 14: islamic.v1.FaqMediaItem
	(*FaqListReq)(nil),          // 15: islamic.v1.FaqListReq
	(*common.Error)(nil),        // 16: common.Error
	(*common.PageRequest)(nil),  // 17: common.PageRequest
	(*common.PageResponse)(nil), // 18: common.PageResponse
}
var file_islamic_v1_faq_proto_depIdxs = []int32{
	3,  // 0: islamic.v1.FaqCateListData.list:type_name -> islamic.v1.FaqCateItem
	16, // 1: islamic.v1.FaqCateListRes.error:type_name -> common.Error
	1,  // 2: islamic.v1.FaqCateListRes.data:type_name -> islamic.v1.FaqCateListData
	17, // 3: islamic.v1.FaqListByCateIdReq.page:type_name -> common.PageRequest
	7,  // 4: islamic.v1.FaqListByCateIdData.list:type_name -> islamic.v1.FaqQuestionItem
	18, // 5: islamic.v1.FaqListByCateIdData.page:type_name -> common.PageResponse
	16, // 6: islamic.v1.FaqListByCateIdRes.error:type_name -> common.Error
	5,  // 7: islamic.v1.FaqListByCateIdRes.data:type_name -> islamic.v1.FaqListByCateIdData
	16, // 8: islamic.v1.FaqOneRes.error:type_name -> common.Error
	10, // 9: islamic.v1.FaqOneRes.data:type_name -> islamic.v1.FaqQuestionOneItem
	16, // 10: islamic.v1.FaqMediaRes.error:type_name -> common.Error
	13, // 11: islamic.v1.FaqMediaRes.data:type_name -> islamic.v1.FaqMediaData
	14, // 12: islamic.v1.FaqMediaData.list:type_name -> islamic.v1.FaqMediaItem
	17, // 13: islamic.v1.FaqListReq.page:type_name -> common.PageRequest
	0,  // 14: islamic.v1.FaqService.FaqCategoryList:input_type -> islamic.v1.FaqCateListReq
	4,  // 15: islamic.v1.FaqService.FaqListByCateId:input_type -> islamic.v1.FaqListByCateIdReq
	8,  // 16: islamic.v1.FaqService.FaqOne:input_type -> islamic.v1.FaqOneReq
	11, // 17: islamic.v1.FaqService.FaqMedia:input_type -> islamic.v1.FaqMediaReq
	15, // 18: islamic.v1.FaqService.FaqList:input_type -> islamic.v1.FaqListReq
	2,  // 19: islamic.v1.FaqService.FaqCategoryList:output_type -> islamic.v1.FaqCateListRes
	6,  // 20: islamic.v1.FaqService.FaqListByCateId:output_type -> islamic.v1.FaqListByCateIdRes
	9,  // 21: islamic.v1.FaqService.FaqOne:output_type -> islamic.v1.FaqOneRes
	12, // 22: islamic.v1.FaqService.FaqMedia:output_type -> islamic.v1.FaqMediaRes
	6,  // 23: islamic.v1.FaqService.FaqList:output_type -> islamic.v1.FaqListByCateIdRes
	19, // [19:24] is the sub-list for method output_type
	14, // [14:19] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_islamic_v1_faq_proto_init() }
func file_islamic_v1_faq_proto_init() {
	if File_islamic_v1_faq_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_faq_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqCateListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqCateListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqCateListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqCateItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqListByCateIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqListByCateIdData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqListByCateIdRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqQuestionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqOneRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqQuestionOneItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqMediaReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqMediaRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqMediaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqMediaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_faq_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_faq_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_faq_proto_goTypes,
		DependencyIndexes: file_islamic_v1_faq_proto_depIdxs,
		MessageInfos:      file_islamic_v1_faq_proto_msgTypes,
	}.Build()
	File_islamic_v1_faq_proto = out.File
	file_islamic_v1_faq_proto_rawDesc = nil
	file_islamic_v1_faq_proto_goTypes = nil
	file_islamic_v1_faq_proto_depIdxs = nil
}
