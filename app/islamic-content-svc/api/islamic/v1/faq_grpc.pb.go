// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// FaqServiceClient is the client API for FaqService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FaqServiceClient interface {
	// FAQ分类列表
	FaqCategoryList(ctx context.Context, in *FaqCateListReq, opts ...grpc.CallOption) (*FaqCateListRes, error)
	// FAQ列表通过分类ID
	FaqListByCateId(ctx context.Context, in *FaqListByCateIdReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error)
	// FAQ详情
	FaqOne(ctx context.Context, in *FaqOneReq, opts ...grpc.CallOption) (*FaqOneRes, error)
	// 社交媒体
	FaqMedia(ctx context.Context, in *FaqMediaReq, opts ...grpc.CallOption) (*FaqMediaRes, error)
	// FAQ列表无分类ID
	FaqList(ctx context.Context, in *FaqListReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error)
}

type faqServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFaqServiceClient(cc grpc.ClientConnInterface) FaqServiceClient {
	return &faqServiceClient{cc}
}

func (c *faqServiceClient) FaqCategoryList(ctx context.Context, in *FaqCateListReq, opts ...grpc.CallOption) (*FaqCateListRes, error) {
	out := new(FaqCateListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FaqService/FaqCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqListByCateId(ctx context.Context, in *FaqListByCateIdReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error) {
	out := new(FaqListByCateIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FaqService/FaqListByCateId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqOne(ctx context.Context, in *FaqOneReq, opts ...grpc.CallOption) (*FaqOneRes, error) {
	out := new(FaqOneRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FaqService/FaqOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqMedia(ctx context.Context, in *FaqMediaReq, opts ...grpc.CallOption) (*FaqMediaRes, error) {
	out := new(FaqMediaRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FaqService/FaqMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faqServiceClient) FaqList(ctx context.Context, in *FaqListReq, opts ...grpc.CallOption) (*FaqListByCateIdRes, error) {
	out := new(FaqListByCateIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.FaqService/FaqList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FaqServiceServer is the server API for FaqService service.
// All implementations must embed UnimplementedFaqServiceServer
// for forward compatibility
type FaqServiceServer interface {
	// FAQ分类列表
	FaqCategoryList(context.Context, *FaqCateListReq) (*FaqCateListRes, error)
	// FAQ列表通过分类ID
	FaqListByCateId(context.Context, *FaqListByCateIdReq) (*FaqListByCateIdRes, error)
	// FAQ详情
	FaqOne(context.Context, *FaqOneReq) (*FaqOneRes, error)
	// 社交媒体
	FaqMedia(context.Context, *FaqMediaReq) (*FaqMediaRes, error)
	// FAQ列表无分类ID
	FaqList(context.Context, *FaqListReq) (*FaqListByCateIdRes, error)
	mustEmbedUnimplementedFaqServiceServer()
}

// UnimplementedFaqServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFaqServiceServer struct {
}

func (UnimplementedFaqServiceServer) FaqCategoryList(context.Context, *FaqCateListReq) (*FaqCateListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqCategoryList not implemented")
}
func (UnimplementedFaqServiceServer) FaqListByCateId(context.Context, *FaqListByCateIdReq) (*FaqListByCateIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqListByCateId not implemented")
}
func (UnimplementedFaqServiceServer) FaqOne(context.Context, *FaqOneReq) (*FaqOneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqOne not implemented")
}
func (UnimplementedFaqServiceServer) FaqMedia(context.Context, *FaqMediaReq) (*FaqMediaRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqMedia not implemented")
}
func (UnimplementedFaqServiceServer) FaqList(context.Context, *FaqListReq) (*FaqListByCateIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaqList not implemented")
}
func (UnimplementedFaqServiceServer) mustEmbedUnimplementedFaqServiceServer() {}

// UnsafeFaqServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FaqServiceServer will
// result in compilation errors.
type UnsafeFaqServiceServer interface {
	mustEmbedUnimplementedFaqServiceServer()
}

func RegisterFaqServiceServer(s *grpc.Server, srv FaqServiceServer) {
	s.RegisterService(&_FaqService_serviceDesc, srv)
}

func _FaqService_FaqCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqCateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FaqService/FaqCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqCategoryList(ctx, req.(*FaqCateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqListByCateId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqListByCateIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqListByCateId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FaqService/FaqListByCateId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqListByCateId(ctx, req.(*FaqListByCateIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FaqService/FaqOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqOne(ctx, req.(*FaqOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqMediaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FaqService/FaqMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqMedia(ctx, req.(*FaqMediaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaqService_FaqList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaqListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaqServiceServer).FaqList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.FaqService/FaqList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaqServiceServer).FaqList(ctx, req.(*FaqListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FaqService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.FaqService",
	HandlerType: (*FaqServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FaqCategoryList",
			Handler:    _FaqService_FaqCategoryList_Handler,
		},
		{
			MethodName: "FaqListByCateId",
			Handler:    _FaqService_FaqListByCateId_Handler,
		},
		{
			MethodName: "FaqOne",
			Handler:    _FaqService_FaqOne_Handler,
		},
		{
			MethodName: "FaqMedia",
			Handler:    _FaqService_FaqMedia_Handler,
		},
		{
			MethodName: "FaqList",
			Handler:    _FaqService_FaqList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/faq.proto",
}
