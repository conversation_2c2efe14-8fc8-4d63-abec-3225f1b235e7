// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// VideoServiceClient is the client API for VideoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VideoServiceClient interface {
	// playlist
	PlaylistList(ctx context.Context, in *PlaylistListReq, opts ...grpc.CallOption) (*PlaylistListRes, error)
	// video
	VideoList(ctx context.Context, in *VideoListReq, opts ...grpc.CallOption) (*VideoListRes, error)
	VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error)
	RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error)
	// video collect
	VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error)
	VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error)
	VideoCollectStatusCheck(ctx context.Context, in *VideoCollectStatusCheckReq, opts ...grpc.CallOption) (*VideoCollectStatusCheckRes, error)
	// video share
	VideoShare(ctx context.Context, in *VideoShareReq, opts ...grpc.CallOption) (*VideoShareRes, error)
}

type videoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVideoServiceClient(cc grpc.ClientConnInterface) VideoServiceClient {
	return &videoServiceClient{cc}
}

func (c *videoServiceClient) PlaylistList(ctx context.Context, in *PlaylistListReq, opts ...grpc.CallOption) (*PlaylistListRes, error) {
	out := new(PlaylistListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/PlaylistList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoList(ctx context.Context, in *VideoListReq, opts ...grpc.CallOption) (*VideoListRes, error) {
	out := new(VideoListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error) {
	out := new(VideoDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error) {
	out := new(RecommendedVideoListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/RecommendedVideoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error) {
	out := new(VideoCollectRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error) {
	out := new(VideoCollectListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoCollectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollectStatusCheck(ctx context.Context, in *VideoCollectStatusCheckReq, opts ...grpc.CallOption) (*VideoCollectStatusCheckRes, error) {
	out := new(VideoCollectStatusCheckRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoCollectStatusCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoShare(ctx context.Context, in *VideoShareReq, opts ...grpc.CallOption) (*VideoShareRes, error) {
	out := new(VideoShareRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.VideoService/VideoShare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoServiceServer is the server API for VideoService service.
// All implementations must embed UnimplementedVideoServiceServer
// for forward compatibility
type VideoServiceServer interface {
	// playlist
	PlaylistList(context.Context, *PlaylistListReq) (*PlaylistListRes, error)
	// video
	VideoList(context.Context, *VideoListReq) (*VideoListRes, error)
	VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error)
	RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error)
	// video collect
	VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error)
	VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error)
	VideoCollectStatusCheck(context.Context, *VideoCollectStatusCheckReq) (*VideoCollectStatusCheckRes, error)
	// video share
	VideoShare(context.Context, *VideoShareReq) (*VideoShareRes, error)
	mustEmbedUnimplementedVideoServiceServer()
}

// UnimplementedVideoServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVideoServiceServer struct {
}

func (UnimplementedVideoServiceServer) PlaylistList(context.Context, *PlaylistListReq) (*PlaylistListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaylistList not implemented")
}
func (UnimplementedVideoServiceServer) VideoList(context.Context, *VideoListReq) (*VideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoDetail not implemented")
}
func (UnimplementedVideoServiceServer) RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendedVideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollect not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollectList not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollectStatusCheck(context.Context, *VideoCollectStatusCheckReq) (*VideoCollectStatusCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollectStatusCheck not implemented")
}
func (UnimplementedVideoServiceServer) VideoShare(context.Context, *VideoShareReq) (*VideoShareRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoShare not implemented")
}
func (UnimplementedVideoServiceServer) mustEmbedUnimplementedVideoServiceServer() {}

// UnsafeVideoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VideoServiceServer will
// result in compilation errors.
type UnsafeVideoServiceServer interface {
	mustEmbedUnimplementedVideoServiceServer()
}

func RegisterVideoServiceServer(s *grpc.Server, srv VideoServiceServer) {
	s.RegisterService(&_VideoService_serviceDesc, srv)
}

func _VideoService_PlaylistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaylistListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).PlaylistList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/PlaylistList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).PlaylistList(ctx, req.(*PlaylistListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoList(ctx, req.(*VideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoDetail(ctx, req.(*VideoDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_RecommendedVideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendedVideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/RecommendedVideoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, req.(*RecommendedVideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollect(ctx, req.(*VideoCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoCollectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollectList(ctx, req.(*VideoCollectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollectStatusCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectStatusCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollectStatusCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoCollectStatusCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollectStatusCheck(ctx, req.(*VideoCollectStatusCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.VideoService/VideoShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoShare(ctx, req.(*VideoShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VideoService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.VideoService",
	HandlerType: (*VideoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlaylistList",
			Handler:    _VideoService_PlaylistList_Handler,
		},
		{
			MethodName: "VideoList",
			Handler:    _VideoService_VideoList_Handler,
		},
		{
			MethodName: "VideoDetail",
			Handler:    _VideoService_VideoDetail_Handler,
		},
		{
			MethodName: "RecommendedVideoList",
			Handler:    _VideoService_RecommendedVideoList_Handler,
		},
		{
			MethodName: "VideoCollect",
			Handler:    _VideoService_VideoCollect_Handler,
		},
		{
			MethodName: "VideoCollectList",
			Handler:    _VideoService_VideoCollectList_Handler,
		},
		{
			MethodName: "VideoCollectStatusCheck",
			Handler:    _VideoService_VideoCollectStatusCheck_Handler,
		},
		{
			MethodName: "VideoShare",
			Handler:    _VideoService_VideoShare_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/video.proto",
}
