// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PrayerServiceClient is the client API for PrayerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PrayerServiceClient interface {
	// 获取日历数据
	GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error)
	// 获取朝觐日程列表
	GetHajiJadwalList(ctx context.Context, in *HajiJadwalListReq, opts ...grpc.CallOption) (*HajiJadwalListRes, error)
	// 获取朝觐日程详情
	GetHajiJadwalDetail(ctx context.Context, in *HajiJadwalDetailReq, opts ...grpc.CallOption) (*HajiJadwalDetailRes, error)
	// 获取朝觐仪式顺序列表
	GetHajiUrutanList(ctx context.Context, in *HajiUrutanListReq, opts ...grpc.CallOption) (*HajiUrutanListRes, error)
	// 获取朝觐仪式顺序详情
	GetHajiUrutanDetail(ctx context.Context, in *HajiUrutanDetailReq, opts ...grpc.CallOption) (*HajiUrutanDetailRes, error)
	// 获取朝觐祈祷文简要列表
	GetHajiDoaRingkasList(ctx context.Context, in *HajiDoaRingkasListReq, opts ...grpc.CallOption) (*HajiDoaRingkasListRes, error)
	// 获取朝觐祈祷文详细列表
	GetHajiDoaPanjangList(ctx context.Context, in *HajiDoaPanjangListReq, opts ...grpc.CallOption) (*HajiDoaPanjangListRes, error)
	// 获取朝觐祈祷文诵读内容列表
	GetHajiDoaPanjangBacaanList(ctx context.Context, in *HajiDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*HajiDoaPanjangBacaanListRes, error)
	// 通用的 Doa Bacaan 详情接口
	GetCommonDoaBacaanDetail(ctx context.Context, in *CommonDoaBacaanDetailReq, opts ...grpc.CallOption) (*CommonDoaBacaanDetailRes, error)
	// 获取朝觐智慧列表
	GetHajiHikmahList(ctx context.Context, in *HajiHikmahListReq, opts ...grpc.CallOption) (*HajiHikmahListRes, error)
	// 获取朝觐新闻列表
	GetHajiNewsList(ctx context.Context, in *HajiNewsListReq, opts ...grpc.CallOption) (*HajiNewsListRes, error)
	// 获取朝觐地标列表
	GetHajiLandmarkList(ctx context.Context, in *HajiLandmarkListReq, opts ...grpc.CallOption) (*HajiLandmarkListRes, error)
	// 获取朝觐地标详情
	GetHajiLandmarkDetail(ctx context.Context, in *HajiLandmarkDetailReq, opts ...grpc.CallOption) (*HajiLandmarkDetailRes, error)
	// 获取副朝仪式顺序列表
	GetUmrahUrutanList(ctx context.Context, in *UmrahUrutanListReq, opts ...grpc.CallOption) (*UmrahUrutanListRes, error)
	// 获取副朝仪式顺序详情
	GetUmrahUrutanDetail(ctx context.Context, in *UmrahUrutanDetailReq, opts ...grpc.CallOption) (*UmrahUrutanDetailRes, error)
	// 获取副朝祈祷文简要列表
	GetUmrahDoaRingkasList(ctx context.Context, in *UmrahDoaRingkasListReq, opts ...grpc.CallOption) (*UmrahDoaRingkasListRes, error)
	// 获取副朝祈祷文详细列表
	GetUmrahDoaPanjangList(ctx context.Context, in *UmrahDoaPanjangListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangListRes, error)
	// 获取副朝祈祷文诵读内容列表
	GetUmrahDoaPanjangBacaanList(ctx context.Context, in *UmrahDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangBacaanListRes, error)
	// 获取副朝智慧列表
	GetUmrahHikmahList(ctx context.Context, in *UmrahHikmahListReq, opts ...grpc.CallOption) (*UmrahHikmahListRes, error)
	// 获取副朝地标列表
	GetUmrahLandmarkList(ctx context.Context, in *UmrahLandmarkListReq, opts ...grpc.CallOption) (*UmrahLandmarkListRes, error)
	// 获取副朝地标详情
	GetUmrahLandmarkDetail(ctx context.Context, in *UmrahLandmarkDetailReq, opts ...grpc.CallOption) (*UmrahLandmarkDetailRes, error)
	// 获取斋月祈祷文简要列表
	GetRamadhanDoaList(ctx context.Context, in *RamadhanDoaListReq, opts ...grpc.CallOption) (*RamadhanDoaListRes, error)
}

type prayerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrayerServiceClient(cc grpc.ClientConnInterface) PrayerServiceClient {
	return &prayerServiceClient{cc}
}

func (c *prayerServiceClient) GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error) {
	out := new(CalendarRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetCalendar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error) {
	out := new(BatchCalendarRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetBatchCalendar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error) {
	out := new(GetDailyPrayerTimeRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetDailyPrayerTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error) {
	out := new(GetMonthlyPrayerTimesRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetMonthlyPrayerTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiJadwalList(ctx context.Context, in *HajiJadwalListReq, opts ...grpc.CallOption) (*HajiJadwalListRes, error) {
	out := new(HajiJadwalListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiJadwalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiJadwalDetail(ctx context.Context, in *HajiJadwalDetailReq, opts ...grpc.CallOption) (*HajiJadwalDetailRes, error) {
	out := new(HajiJadwalDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiJadwalDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiUrutanList(ctx context.Context, in *HajiUrutanListReq, opts ...grpc.CallOption) (*HajiUrutanListRes, error) {
	out := new(HajiUrutanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiUrutanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiUrutanDetail(ctx context.Context, in *HajiUrutanDetailReq, opts ...grpc.CallOption) (*HajiUrutanDetailRes, error) {
	out := new(HajiUrutanDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiUrutanDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaRingkasList(ctx context.Context, in *HajiDoaRingkasListReq, opts ...grpc.CallOption) (*HajiDoaRingkasListRes, error) {
	out := new(HajiDoaRingkasListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiDoaRingkasList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaPanjangList(ctx context.Context, in *HajiDoaPanjangListReq, opts ...grpc.CallOption) (*HajiDoaPanjangListRes, error) {
	out := new(HajiDoaPanjangListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiDoaPanjangList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaPanjangBacaanList(ctx context.Context, in *HajiDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*HajiDoaPanjangBacaanListRes, error) {
	out := new(HajiDoaPanjangBacaanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiDoaPanjangBacaanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetCommonDoaBacaanDetail(ctx context.Context, in *CommonDoaBacaanDetailReq, opts ...grpc.CallOption) (*CommonDoaBacaanDetailRes, error) {
	out := new(CommonDoaBacaanDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetCommonDoaBacaanDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiHikmahList(ctx context.Context, in *HajiHikmahListReq, opts ...grpc.CallOption) (*HajiHikmahListRes, error) {
	out := new(HajiHikmahListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiHikmahList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiNewsList(ctx context.Context, in *HajiNewsListReq, opts ...grpc.CallOption) (*HajiNewsListRes, error) {
	out := new(HajiNewsListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiNewsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiLandmarkList(ctx context.Context, in *HajiLandmarkListReq, opts ...grpc.CallOption) (*HajiLandmarkListRes, error) {
	out := new(HajiLandmarkListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiLandmarkList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiLandmarkDetail(ctx context.Context, in *HajiLandmarkDetailReq, opts ...grpc.CallOption) (*HajiLandmarkDetailRes, error) {
	out := new(HajiLandmarkDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetHajiLandmarkDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahUrutanList(ctx context.Context, in *UmrahUrutanListReq, opts ...grpc.CallOption) (*UmrahUrutanListRes, error) {
	out := new(UmrahUrutanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahUrutanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahUrutanDetail(ctx context.Context, in *UmrahUrutanDetailReq, opts ...grpc.CallOption) (*UmrahUrutanDetailRes, error) {
	out := new(UmrahUrutanDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahUrutanDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaRingkasList(ctx context.Context, in *UmrahDoaRingkasListReq, opts ...grpc.CallOption) (*UmrahDoaRingkasListRes, error) {
	out := new(UmrahDoaRingkasListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahDoaRingkasList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaPanjangList(ctx context.Context, in *UmrahDoaPanjangListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangListRes, error) {
	out := new(UmrahDoaPanjangListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahDoaPanjangList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaPanjangBacaanList(ctx context.Context, in *UmrahDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangBacaanListRes, error) {
	out := new(UmrahDoaPanjangBacaanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahDoaPanjangBacaanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahHikmahList(ctx context.Context, in *UmrahHikmahListReq, opts ...grpc.CallOption) (*UmrahHikmahListRes, error) {
	out := new(UmrahHikmahListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahHikmahList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahLandmarkList(ctx context.Context, in *UmrahLandmarkListReq, opts ...grpc.CallOption) (*UmrahLandmarkListRes, error) {
	out := new(UmrahLandmarkListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahLandmarkList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahLandmarkDetail(ctx context.Context, in *UmrahLandmarkDetailReq, opts ...grpc.CallOption) (*UmrahLandmarkDetailRes, error) {
	out := new(UmrahLandmarkDetailRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetUmrahLandmarkDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetRamadhanDoaList(ctx context.Context, in *RamadhanDoaListReq, opts ...grpc.CallOption) (*RamadhanDoaListRes, error) {
	out := new(RamadhanDoaListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetRamadhanDoaList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrayerServiceServer is the server API for PrayerService service.
// All implementations must embed UnimplementedPrayerServiceServer
// for forward compatibility
type PrayerServiceServer interface {
	// 获取日历数据
	GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error)
	// 获取朝觐日程列表
	GetHajiJadwalList(context.Context, *HajiJadwalListReq) (*HajiJadwalListRes, error)
	// 获取朝觐日程详情
	GetHajiJadwalDetail(context.Context, *HajiJadwalDetailReq) (*HajiJadwalDetailRes, error)
	// 获取朝觐仪式顺序列表
	GetHajiUrutanList(context.Context, *HajiUrutanListReq) (*HajiUrutanListRes, error)
	// 获取朝觐仪式顺序详情
	GetHajiUrutanDetail(context.Context, *HajiUrutanDetailReq) (*HajiUrutanDetailRes, error)
	// 获取朝觐祈祷文简要列表
	GetHajiDoaRingkasList(context.Context, *HajiDoaRingkasListReq) (*HajiDoaRingkasListRes, error)
	// 获取朝觐祈祷文详细列表
	GetHajiDoaPanjangList(context.Context, *HajiDoaPanjangListReq) (*HajiDoaPanjangListRes, error)
	// 获取朝觐祈祷文诵读内容列表
	GetHajiDoaPanjangBacaanList(context.Context, *HajiDoaPanjangBacaanListReq) (*HajiDoaPanjangBacaanListRes, error)
	// 通用的 Doa Bacaan 详情接口
	GetCommonDoaBacaanDetail(context.Context, *CommonDoaBacaanDetailReq) (*CommonDoaBacaanDetailRes, error)
	// 获取朝觐智慧列表
	GetHajiHikmahList(context.Context, *HajiHikmahListReq) (*HajiHikmahListRes, error)
	// 获取朝觐新闻列表
	GetHajiNewsList(context.Context, *HajiNewsListReq) (*HajiNewsListRes, error)
	// 获取朝觐地标列表
	GetHajiLandmarkList(context.Context, *HajiLandmarkListReq) (*HajiLandmarkListRes, error)
	// 获取朝觐地标详情
	GetHajiLandmarkDetail(context.Context, *HajiLandmarkDetailReq) (*HajiLandmarkDetailRes, error)
	// 获取副朝仪式顺序列表
	GetUmrahUrutanList(context.Context, *UmrahUrutanListReq) (*UmrahUrutanListRes, error)
	// 获取副朝仪式顺序详情
	GetUmrahUrutanDetail(context.Context, *UmrahUrutanDetailReq) (*UmrahUrutanDetailRes, error)
	// 获取副朝祈祷文简要列表
	GetUmrahDoaRingkasList(context.Context, *UmrahDoaRingkasListReq) (*UmrahDoaRingkasListRes, error)
	// 获取副朝祈祷文详细列表
	GetUmrahDoaPanjangList(context.Context, *UmrahDoaPanjangListReq) (*UmrahDoaPanjangListRes, error)
	// 获取副朝祈祷文诵读内容列表
	GetUmrahDoaPanjangBacaanList(context.Context, *UmrahDoaPanjangBacaanListReq) (*UmrahDoaPanjangBacaanListRes, error)
	// 获取副朝智慧列表
	GetUmrahHikmahList(context.Context, *UmrahHikmahListReq) (*UmrahHikmahListRes, error)
	// 获取副朝地标列表
	GetUmrahLandmarkList(context.Context, *UmrahLandmarkListReq) (*UmrahLandmarkListRes, error)
	// 获取副朝地标详情
	GetUmrahLandmarkDetail(context.Context, *UmrahLandmarkDetailReq) (*UmrahLandmarkDetailRes, error)
	// 获取斋月祈祷文简要列表
	GetRamadhanDoaList(context.Context, *RamadhanDoaListReq) (*RamadhanDoaListRes, error)
	mustEmbedUnimplementedPrayerServiceServer()
}

// UnimplementedPrayerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPrayerServiceServer struct {
}

func (UnimplementedPrayerServiceServer) GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyPrayerTime not implemented")
}
func (UnimplementedPrayerServiceServer) GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonthlyPrayerTimes not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiJadwalList(context.Context, *HajiJadwalListReq) (*HajiJadwalListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiJadwalList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiJadwalDetail(context.Context, *HajiJadwalDetailReq) (*HajiJadwalDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiJadwalDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiUrutanList(context.Context, *HajiUrutanListReq) (*HajiUrutanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiUrutanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiUrutanDetail(context.Context, *HajiUrutanDetailReq) (*HajiUrutanDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiUrutanDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaRingkasList(context.Context, *HajiDoaRingkasListReq) (*HajiDoaRingkasListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaRingkasList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaPanjangList(context.Context, *HajiDoaPanjangListReq) (*HajiDoaPanjangListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaPanjangList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaPanjangBacaanList(context.Context, *HajiDoaPanjangBacaanListReq) (*HajiDoaPanjangBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaPanjangBacaanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetCommonDoaBacaanDetail(context.Context, *CommonDoaBacaanDetailReq) (*CommonDoaBacaanDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommonDoaBacaanDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiHikmahList(context.Context, *HajiHikmahListReq) (*HajiHikmahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiHikmahList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiNewsList(context.Context, *HajiNewsListReq) (*HajiNewsListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiNewsList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiLandmarkList(context.Context, *HajiLandmarkListReq) (*HajiLandmarkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiLandmarkList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiLandmarkDetail(context.Context, *HajiLandmarkDetailReq) (*HajiLandmarkDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiLandmarkDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahUrutanList(context.Context, *UmrahUrutanListReq) (*UmrahUrutanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahUrutanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahUrutanDetail(context.Context, *UmrahUrutanDetailReq) (*UmrahUrutanDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahUrutanDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaRingkasList(context.Context, *UmrahDoaRingkasListReq) (*UmrahDoaRingkasListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaRingkasList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaPanjangList(context.Context, *UmrahDoaPanjangListReq) (*UmrahDoaPanjangListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaPanjangList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaPanjangBacaanList(context.Context, *UmrahDoaPanjangBacaanListReq) (*UmrahDoaPanjangBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaPanjangBacaanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahHikmahList(context.Context, *UmrahHikmahListReq) (*UmrahHikmahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahHikmahList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahLandmarkList(context.Context, *UmrahLandmarkListReq) (*UmrahLandmarkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahLandmarkList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahLandmarkDetail(context.Context, *UmrahLandmarkDetailReq) (*UmrahLandmarkDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahLandmarkDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetRamadhanDoaList(context.Context, *RamadhanDoaListReq) (*RamadhanDoaListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRamadhanDoaList not implemented")
}
func (UnimplementedPrayerServiceServer) mustEmbedUnimplementedPrayerServiceServer() {}

// UnsafePrayerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrayerServiceServer will
// result in compilation errors.
type UnsafePrayerServiceServer interface {
	mustEmbedUnimplementedPrayerServiceServer()
}

func RegisterPrayerServiceServer(s *grpc.Server, srv PrayerServiceServer) {
	s.RegisterService(&_PrayerService_serviceDesc, srv)
}

func _PrayerService_GetCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetCalendar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetCalendar(ctx, req.(*CalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetBatchCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetBatchCalendar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, req.(*BatchCalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetDailyPrayerTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyPrayerTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetDailyPrayerTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, req.(*GetDailyPrayerTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetMonthlyPrayerTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthlyPrayerTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetMonthlyPrayerTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, req.(*GetMonthlyPrayerTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiJadwalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiJadwalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiJadwalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiJadwalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiJadwalList(ctx, req.(*HajiJadwalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiJadwalDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiJadwalDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiJadwalDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiJadwalDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiJadwalDetail(ctx, req.(*HajiJadwalDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiUrutanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiUrutanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiUrutanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiUrutanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiUrutanList(ctx, req.(*HajiUrutanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiUrutanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiUrutanDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiUrutanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiUrutanDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiUrutanDetail(ctx, req.(*HajiUrutanDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaRingkasList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaRingkasListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaRingkasList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiDoaRingkasList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaRingkasList(ctx, req.(*HajiDoaRingkasListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaPanjangList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaPanjangListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiDoaPanjangList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangList(ctx, req.(*HajiDoaPanjangListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaPanjangBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaPanjangBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiDoaPanjangBacaanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangBacaanList(ctx, req.(*HajiDoaPanjangBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetCommonDoaBacaanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonDoaBacaanDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetCommonDoaBacaanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetCommonDoaBacaanDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetCommonDoaBacaanDetail(ctx, req.(*CommonDoaBacaanDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiHikmahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiHikmahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiHikmahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiHikmahList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiHikmahList(ctx, req.(*HajiHikmahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiNewsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiNewsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiNewsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiNewsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiNewsList(ctx, req.(*HajiNewsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiLandmarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiLandmarkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiLandmarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiLandmarkList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiLandmarkList(ctx, req.(*HajiLandmarkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiLandmarkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiLandmarkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiLandmarkDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetHajiLandmarkDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiLandmarkDetail(ctx, req.(*HajiLandmarkDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahUrutanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahUrutanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahUrutanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahUrutanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahUrutanList(ctx, req.(*UmrahUrutanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahUrutanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahUrutanDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahUrutanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahUrutanDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahUrutanDetail(ctx, req.(*UmrahUrutanDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaRingkasList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaRingkasListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaRingkasList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahDoaRingkasList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaRingkasList(ctx, req.(*UmrahDoaRingkasListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaPanjangList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaPanjangListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahDoaPanjangList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangList(ctx, req.(*UmrahDoaPanjangListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaPanjangBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaPanjangBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahDoaPanjangBacaanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangBacaanList(ctx, req.(*UmrahDoaPanjangBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahHikmahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahHikmahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahHikmahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahHikmahList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahHikmahList(ctx, req.(*UmrahHikmahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahLandmarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahLandmarkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahLandmarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahLandmarkList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahLandmarkList(ctx, req.(*UmrahLandmarkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahLandmarkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahLandmarkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahLandmarkDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetUmrahLandmarkDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahLandmarkDetail(ctx, req.(*UmrahLandmarkDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetRamadhanDoaList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RamadhanDoaListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetRamadhanDoaList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetRamadhanDoaList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetRamadhanDoaList(ctx, req.(*RamadhanDoaListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PrayerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.PrayerService",
	HandlerType: (*PrayerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCalendar",
			Handler:    _PrayerService_GetCalendar_Handler,
		},
		{
			MethodName: "GetBatchCalendar",
			Handler:    _PrayerService_GetBatchCalendar_Handler,
		},
		{
			MethodName: "GetDailyPrayerTime",
			Handler:    _PrayerService_GetDailyPrayerTime_Handler,
		},
		{
			MethodName: "GetMonthlyPrayerTimes",
			Handler:    _PrayerService_GetMonthlyPrayerTimes_Handler,
		},
		{
			MethodName: "GetHajiJadwalList",
			Handler:    _PrayerService_GetHajiJadwalList_Handler,
		},
		{
			MethodName: "GetHajiJadwalDetail",
			Handler:    _PrayerService_GetHajiJadwalDetail_Handler,
		},
		{
			MethodName: "GetHajiUrutanList",
			Handler:    _PrayerService_GetHajiUrutanList_Handler,
		},
		{
			MethodName: "GetHajiUrutanDetail",
			Handler:    _PrayerService_GetHajiUrutanDetail_Handler,
		},
		{
			MethodName: "GetHajiDoaRingkasList",
			Handler:    _PrayerService_GetHajiDoaRingkasList_Handler,
		},
		{
			MethodName: "GetHajiDoaPanjangList",
			Handler:    _PrayerService_GetHajiDoaPanjangList_Handler,
		},
		{
			MethodName: "GetHajiDoaPanjangBacaanList",
			Handler:    _PrayerService_GetHajiDoaPanjangBacaanList_Handler,
		},
		{
			MethodName: "GetCommonDoaBacaanDetail",
			Handler:    _PrayerService_GetCommonDoaBacaanDetail_Handler,
		},
		{
			MethodName: "GetHajiHikmahList",
			Handler:    _PrayerService_GetHajiHikmahList_Handler,
		},
		{
			MethodName: "GetHajiNewsList",
			Handler:    _PrayerService_GetHajiNewsList_Handler,
		},
		{
			MethodName: "GetHajiLandmarkList",
			Handler:    _PrayerService_GetHajiLandmarkList_Handler,
		},
		{
			MethodName: "GetHajiLandmarkDetail",
			Handler:    _PrayerService_GetHajiLandmarkDetail_Handler,
		},
		{
			MethodName: "GetUmrahUrutanList",
			Handler:    _PrayerService_GetUmrahUrutanList_Handler,
		},
		{
			MethodName: "GetUmrahUrutanDetail",
			Handler:    _PrayerService_GetUmrahUrutanDetail_Handler,
		},
		{
			MethodName: "GetUmrahDoaRingkasList",
			Handler:    _PrayerService_GetUmrahDoaRingkasList_Handler,
		},
		{
			MethodName: "GetUmrahDoaPanjangList",
			Handler:    _PrayerService_GetUmrahDoaPanjangList_Handler,
		},
		{
			MethodName: "GetUmrahDoaPanjangBacaanList",
			Handler:    _PrayerService_GetUmrahDoaPanjangBacaanList_Handler,
		},
		{
			MethodName: "GetUmrahHikmahList",
			Handler:    _PrayerService_GetUmrahHikmahList_Handler,
		},
		{
			MethodName: "GetUmrahLandmarkList",
			Handler:    _PrayerService_GetUmrahLandmarkList_Handler,
		},
		{
			MethodName: "GetUmrahLandmarkDetail",
			Handler:    _PrayerService_GetUmrahLandmarkDetail_Handler,
		},
		{
			MethodName: "GetRamadhanDoaList",
			Handler:    _PrayerService_GetRamadhanDoaList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/prayer.proto",
}
