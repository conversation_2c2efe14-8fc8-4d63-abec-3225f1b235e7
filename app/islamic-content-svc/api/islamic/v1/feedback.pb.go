// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/feedback.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FeedbackAddReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeedbackType int32             `protobuf:"varint,1,opt,name=feedback_type,json=feedbackType,proto3" json:"feedback_type,omitempty" dc:"反馈类型 1-问题反馈,2-建议反馈"` // 反馈类型 1-问题反馈,2-建议反馈
	Desc         string            `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty" dc:"反馈描述"`                                                    // 反馈描述
	Images       []*FeedBackImages `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty" dc:"反馈图片"`                                                // 反馈图片
}

func (x *FeedbackAddReq) Reset() {
	*x = FeedbackAddReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_feedback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackAddReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackAddReq) ProtoMessage() {}

func (x *FeedbackAddReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackAddReq.ProtoReflect.Descriptor instead.
func (*FeedbackAddReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{0}
}

func (x *FeedbackAddReq) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *FeedbackAddReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FeedbackAddReq) GetImages() []*FeedBackImages {
	if x != nil {
		return x.Images
	}
	return nil
}

type FeedBackImages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Key  string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *FeedBackImages) Reset() {
	*x = FeedBackImages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_feedback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedBackImages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedBackImages) ProtoMessage() {}

func (x *FeedBackImages) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedBackImages.ProtoReflect.Descriptor instead.
func (*FeedBackImages) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{1}
}

func (x *FeedBackImages) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FeedBackImages) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FeedBackImages) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type FeedbackAddRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *FeedbackAddRes) Reset() {
	*x = FeedbackAddRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_feedback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackAddRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackAddRes) ProtoMessage() {}

func (x *FeedbackAddRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackAddRes.ProtoReflect.Descriptor instead.
func (*FeedbackAddRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{2}
}

func (x *FeedbackAddRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FeedbackAddRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FeedbackAddRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_islamic_v1_feedback_proto protoreflect.FileDescriptor

var file_islamic_v1_feedback_proto_rawDesc = []byte{
	0x0a, 0x19, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x0e, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x32, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0x48, 0x0a, 0x0e, 0x46, 0x65, 0x65,
	0x64, 0x42, 0x61, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x5b, 0x0a, 0x0e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x41,
	0x64, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x32, 0x58, 0x0a, 0x0f, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x41,
	0x64, 0x64, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x52, 0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61,
	0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_feedback_proto_rawDescOnce sync.Once
	file_islamic_v1_feedback_proto_rawDescData = file_islamic_v1_feedback_proto_rawDesc
)

func file_islamic_v1_feedback_proto_rawDescGZIP() []byte {
	file_islamic_v1_feedback_proto_rawDescOnce.Do(func() {
		file_islamic_v1_feedback_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_feedback_proto_rawDescData)
	})
	return file_islamic_v1_feedback_proto_rawDescData
}

var file_islamic_v1_feedback_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_islamic_v1_feedback_proto_goTypes = []interface{}{
	(*FeedbackAddReq)(nil), // 0: islamic.v1.FeedbackAddReq
	(*FeedBackImages)(nil), // 1: islamic.v1.FeedBackImages
	(*FeedbackAddRes)(nil), // 2: islamic.v1.FeedbackAddRes
	(*common.Error)(nil),   // 3: common.Error
}
var file_islamic_v1_feedback_proto_depIdxs = []int32{
	1, // 0: islamic.v1.FeedbackAddReq.images:type_name -> islamic.v1.FeedBackImages
	3, // 1: islamic.v1.FeedbackAddRes.error:type_name -> common.Error
	0, // 2: islamic.v1.FeedbackService.FeedbackAdd:input_type -> islamic.v1.FeedbackAddReq
	2, // 3: islamic.v1.FeedbackService.FeedbackAdd:output_type -> islamic.v1.FeedbackAddRes
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_islamic_v1_feedback_proto_init() }
func file_islamic_v1_feedback_proto_init() {
	if File_islamic_v1_feedback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_feedback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackAddReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_feedback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedBackImages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_feedback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackAddRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_feedback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_feedback_proto_goTypes,
		DependencyIndexes: file_islamic_v1_feedback_proto_depIdxs,
		MessageInfos:      file_islamic_v1_feedback_proto_msgTypes,
	}.Build()
	File_islamic_v1_feedback_proto = out.File
	file_islamic_v1_feedback_proto_rawDesc = nil
	file_islamic_v1_feedback_proto_goTypes = nil
	file_islamic_v1_feedback_proto_depIdxs = nil
}
